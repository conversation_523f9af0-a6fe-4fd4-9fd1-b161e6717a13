# 🎓 Guide d'Utilisation - Campus Connect

Bienvenue dans Campus Connect ! Ce guide vous aidera à explorer toutes les fonctionnalités de l'application.

## 🚀 **Accès à l'Application**

### **URL de l'Application**
- **Local** : http://localhost:8080/
- **<PERSON><PERSON><PERSON>** : http://**************:8080/

### **Première Connexion**
L'application démarre automatiquement avec un utilisateur étudiant par défaut.

---

## 🔄 **Changement de Rôle (RoleSwitcher)**

### **Comment Changer de Rôle**
1. **Localiser le RoleSwitcher** dans la sidebar (bouton avec icône de rôle + "Settings")
2. **Cliquer** pour ouvrir le sélecteur
3. **Choisir un rôle** parmi les 3 disponibles

### **Rôles Disponibles**

#### 🎓 **Étudiant (<PERSON>)**
- **Email** : <EMAIL>
- **Fonctionnalités** :
  - Dashboard personnalisé avec gamification
  - Suivi des cours et notes
  - Planificateur d'études
  - Forum et peer review
  - Système de badges
  - Calculatrice GPA

#### 👨‍🏫 **Enseignant (<PERSON>)**
- **Email** : <EMAIL>
- **Fonctionnalités** :
  - Gestion des cours
  - Notation et évaluations
  - Suivi des étudiants
  - Analytics de performance
  - Forum Q&A
  - Planning des cours

#### 🛡️ **Administrateur (Sophie Admin)**
- **Email** : <EMAIL>
- **Fonctionnalités** :
  - Gestion des utilisateurs
  - Statistiques globales
  - Configuration système
  - Rapports et analytics
  - Approbations
  - Monitoring système

---

## 🎨 **Thèmes et Interface**

### **Changement de Thème**
- **Bouton de thème** dans la sidebar (icône Soleil/Lune)
- **Modes disponibles** : Clair, Sombre, Système
- **Persistance** : Le thème choisi est sauvegardé

### **Navigation**
- **Sidebar responsive** : Se réduit automatiquement sur mobile
- **Navigation conditionnelle** : Les éléments changent selon le rôle
- **Breadcrumbs** : Navigation contextuelle

---

## 📊 **Dashboards Spécialisés**

### 🎓 **Dashboard Étudiant**
#### **Sections Principales**
- **Statistiques personnelles** : GPA, heures d'étude, streak
- **Actions rapides** : Session d'étude, notes, forum, achievements
- **Devoirs à venir** : Liste avec priorités et progression
- **Planning du jour** : Emploi du temps personnalisé
- **Succès récents** : Badges et accomplissements
- **Citation motivationnelle** : Inspiration quotidienne

#### **Fonctionnalités Interactives**
- **Barres de progression animées** pour les devoirs
- **Cartes flottantes** avec effets hover
- **Compteurs animés** pour les statistiques
- **Fond animé** avec vagues relaxantes

### 👨‍🏫 **Dashboard Enseignant**
#### **Onglets Disponibles**
1. **Vue d'ensemble** : Métriques principales
2. **Mes Cours** : Gestion des cours
3. **Étudiants** : Suivi des étudiants
4. **Planning** : Emploi du temps

#### **Fonctionnalités Clés**
- **Actions rapides** : Créer devoirs, noter, forum Q&A
- **Performance des cours** : Statistiques détaillées
- **Top étudiants** : Classement par performance
- **Planning du jour** : Cours et consultations

### 🛡️ **Dashboard Administrateur**
#### **Sections Principales**
- **Métriques globales** : Utilisateurs, système, croissance
- **Approbations en attente** : Candidatures et demandes
- **Monitoring système** : CPU, mémoire, stockage
- **Analytics avancées** : Rapports et statistiques

#### **Pages Spécialisées**
- **Analytics** (`/dashboard/admin/analytics`)
- **Rapports** (`/dashboard/admin/reports`)
- **Monitoring** (`/dashboard/admin/monitoring`)

---

## 📈 **Pages Analytics Administrateur**

### 📊 **Analytics** (`/dashboard/admin/analytics`)
#### **Onglets Disponibles**
1. **Vue d'ensemble** : Métriques principales et graphiques
2. **Utilisateurs** : Analytics détaillées des utilisateurs
3. **Cours** : Performance et statistiques des cours
4. **Système** : Monitoring technique
5. **Rapports** : Génération de rapports

#### **Fonctionnalités**
- **Métriques temps réel** : Mise à jour automatique
- **Graphiques animés** : Visualisations interactives
- **Filtres temporels** : Semaine, mois, trimestre, année
- **Export de données** : PDF, Excel, CSV

### 📋 **Rapports** (`/dashboard/admin/reports`)
#### **Types de Rapports**
- **Rapport Mensuel Complet** : Statistiques globales
- **Performance des Étudiants** : Analyses académiques
- **Utilisation de la Plateforme** : Métriques d'usage
- **Rapport Financier** : Analyses économiques
- **Engagement des Utilisateurs** : Rétention et satisfaction

#### **Fonctionnalités**
- **Génération à la demande** : Rapports personnalisés
- **Historique** : Rapports précédemment générés
- **Partage** : Email, liens, téléchargement
- **Programmation** : Rapports automatiques

### 🖥️ **Monitoring** (`/dashboard/admin/monitoring`)
#### **Métriques Surveillées**
- **Serveur** : CPU, mémoire, stockage, réseau
- **Application** : Utilisateurs actifs, temps de réponse
- **Base de données** : Connexions, requêtes, sauvegardes
- **Sécurité** : Tentatives de connexion, vulnérabilités

#### **Fonctionnalités**
- **Temps réel** : Mise à jour toutes les 5 secondes
- **Alertes** : Notifications automatiques
- **Historique** : Graphiques sur 24h
- **Seuils** : Configuration des alertes

---

## 📚 **Pages Fonctionnelles**

### 📖 **Resources** (`/dashboard/resources`)
- **Recherche** : Filtrage par nom, type, cours
- **Types** : Documents, vidéos, images, liens, livres
- **Téléchargement** : Accès direct aux fichiers
- **Mode sombre** : Interface adaptative

### 🏆 **Achievements** (`/dashboard/achievements`)
- **Badges** : Système de récompenses
- **Progression** : Suivi des accomplissements
- **Classements** : Comparaison avec les pairs

### 📊 **Study Planner** (`/dashboard/study-planner`)
- **Planification** : Organisation des sessions d'étude
- **Pomodoro** : Technique de productivité
- **Suivi** : Historique des sessions

---

## 🎮 **Fonctionnalités Interactives**

### 🎨 **Animations GSAP**
- **Entrées de page** : Animations fluides
- **Hover effects** : Cartes interactives (lift, glow, tilt, scale)
- **Graphiques** : Visualisations animées
- **Transitions** : Changements d'état fluides

### 🎯 **Gamification**
- **Système de badges** : Récompenses pour les accomplissements
- **Streaks** : Jours consécutifs d'activité
- **Points XP** : Système de progression
- **Classements** : Compétition amicale

### 📱 **Responsive Design**
- **Mobile** : Interface optimisée pour smartphones
- **Tablet** : Adaptation pour tablettes
- **Desktop** : Expérience complète sur ordinateur

---

## 🧪 **Tests Recommandés**

### **1. Test des Rôles**
1. **Démarrer** en tant qu'étudiant
2. **Explorer** le dashboard étudiant
3. **Changer** vers enseignant avec le RoleSwitcher
4. **Tester** les fonctionnalités enseignant
5. **Basculer** vers administrateur
6. **Explorer** les pages analytics

### **2. Test des Thèmes**
1. **Mode clair** : Interface par défaut
2. **Mode sombre** : Basculer avec le bouton thème
3. **Vérifier** toutes les pages en mode sombre
4. **Tester** la persistance (rafraîchir la page)

### **3. Test de Navigation**
1. **Sidebar** : Tester tous les liens
2. **Responsive** : Redimensionner la fenêtre
3. **Mobile** : Tester sur petit écran
4. **Breadcrumbs** : Navigation contextuelle

### **4. Test des Analytics (Admin)**
1. **Basculer** vers le rôle administrateur
2. **Naviguer** vers Analytics
3. **Tester** tous les onglets
4. **Générer** un rapport
5. **Vérifier** le monitoring temps réel

---

## 🔧 **Dépannage**

### **Problèmes Courants**
- **Page blanche** : Vérifier la console (F12)
- **Erreur de rôle** : Utiliser le RoleSwitcher
- **Thème incorrect** : Basculer manuellement
- **Navigation** : Rafraîchir la page

### **Console de Développement**
- **F12** : Ouvrir les outils de développement
- **Console** : Voir les erreurs JavaScript
- **Network** : Vérifier les requêtes
- **Elements** : Inspecter le DOM

---

## 🎉 **Fonctionnalités Avancées**

### **Données Temps Réel**
- **Métriques système** : Mise à jour automatique
- **Notifications** : Alertes en temps réel
- **Synchronisation** : État partagé entre onglets

### **Performance**
- **Lazy loading** : Chargement à la demande
- **Animations optimisées** : 60 FPS avec GSAP
- **Cache intelligent** : Réduction des requêtes

### **Accessibilité**
- **Contraste** : Respect des standards WCAG
- **Navigation clavier** : Support complet
- **Screen readers** : Compatibilité

---

## 📞 **Support**

### **Informations Techniques**
- **Framework** : React 18 + TypeScript
- **Styling** : Tailwind CSS + shadcn/ui
- **Animations** : GSAP
- **Routing** : React Router
- **État** : Context API

### **Environnement**
- **Node.js** : Version 18+
- **Package Manager** : npm
- **Build Tool** : Vite
- **Dev Server** : Hot Module Replacement

---

**Profitez de votre exploration de Campus Connect ! 🚀**
