import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import AnimatedBackground from "@/components/animations/AnimatedBackground";
import AnimatedCard from "@/components/animations/AnimatedCard";
import { usePageEntrance } from "@/hooks/useGSAPAnimations";
import { 
  FileText, 
  Download, 
  Calendar, 
  TrendingUp,
  Users,
  BookOpen,
  Activity,
  BarChart3,
  PieChart,
  Filter,
  Search,
  Eye,
  Share,
  Printer,
  Mail,
  Clock,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { Progress } from "@/components/ui/progress";

const AdminReports = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("generated");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedPeriod, setSelectedPeriod] = useState("month");

  // Add page entrance animations
  usePageEntrance([]);

  // Mock reports data
  const reportTemplates = [
    {
      id: "1",
      name: "Rapport Mensuel Complet",
      description: "Statistiques complètes de l'établissement pour le mois",
      category: "general",
      icon: <Calendar className="h-5 w-5" />,
      estimatedTime: "5-10 min",
      lastGenerated: "2024-05-31",
      popularity: 95
    },
    {
      id: "2",
      name: "Performance des Étudiants",
      description: "Analyse détaillée des notes et progressions",
      category: "academic",
      icon: <TrendingUp className="h-5 w-5" />,
      estimatedTime: "3-5 min",
      lastGenerated: "2024-05-28",
      popularity: 87
    },
    {
      id: "3",
      name: "Utilisation de la Plateforme",
      description: "Statistiques d'usage des fonctionnalités",
      category: "usage",
      icon: <Activity className="h-5 w-5" />,
      estimatedTime: "2-3 min",
      lastGenerated: "2024-05-25",
      popularity: 73
    },
    {
      id: "4",
      name: "Rapport Financier",
      description: "Analyse des revenus et dépenses",
      category: "financial",
      icon: <BarChart3 className="h-5 w-5" />,
      estimatedTime: "8-12 min",
      lastGenerated: "2024-05-20",
      popularity: 68
    },
    {
      id: "5",
      name: "Engagement des Utilisateurs",
      description: "Métriques de rétention et satisfaction",
      category: "engagement",
      icon: <Users className="h-5 w-5" />,
      estimatedTime: "4-6 min",
      lastGenerated: "2024-05-15",
      popularity: 82
    },
    {
      id: "6",
      name: "Analyse des Cours",
      description: "Performance et popularité des cours",
      category: "academic",
      icon: <BookOpen className="h-5 w-5" />,
      estimatedTime: "6-8 min",
      lastGenerated: "2024-05-10",
      popularity: 79
    }
  ];

  const generatedReports = [
    {
      id: "1",
      title: "Rapport Mensuel - Mai 2024",
      type: "Rapport Mensuel Complet",
      generatedAt: "2024-05-31 14:30",
      generatedBy: "Sophie Admin",
      size: "2.3 MB",
      format: "PDF",
      downloads: 15,
      status: "completed",
      shared: true
    },
    {
      id: "2",
      title: "Performance Étudiants Q2 2024",
      type: "Performance des Étudiants",
      generatedAt: "2024-05-28 09:15",
      generatedBy: "Sophie Admin",
      size: "1.8 MB",
      format: "Excel",
      downloads: 23,
      status: "completed",
      shared: false
    },
    {
      id: "3",
      title: "Usage Plateforme - Semaine 21",
      type: "Utilisation de la Plateforme",
      generatedAt: "2024-05-25 16:45",
      generatedBy: "Jean Martin",
      size: "1.2 MB",
      format: "PDF",
      downloads: 8,
      status: "completed",
      shared: true
    },
    {
      id: "4",
      title: "Analyse Financière Q2",
      type: "Rapport Financier",
      generatedAt: "2024-05-20 11:20",
      generatedBy: "Sophie Admin",
      size: "3.1 MB",
      format: "PDF",
      downloads: 31,
      status: "completed",
      shared: false
    },
    {
      id: "5",
      title: "Engagement Mai 2024",
      type: "Engagement des Utilisateurs",
      generatedAt: "En cours...",
      generatedBy: "Sophie Admin",
      size: "-",
      format: "PDF",
      downloads: 0,
      status: "generating",
      shared: false
    }
  ];

  const reportCategories = [
    { id: "all", name: "Tous les rapports", count: reportTemplates.length },
    { id: "general", name: "Général", count: 1 },
    { id: "academic", name: "Académique", count: 2 },
    { id: "usage", name: "Utilisation", count: 1 },
    { id: "financial", name: "Financier", count: 1 },
    { id: "engagement", name: "Engagement", count: 1 }
  ];

  const getFilteredTemplates = () => {
    let filtered = reportTemplates;

    if (selectedCategory !== "all") {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(template => 
        template.name.toLowerCase().includes(query) ||
        template.description.toLowerCase().includes(query)
      );
    }

    return filtered;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "generating":
        return <Clock className="h-4 w-4 text-yellow-600 animate-spin" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const generateReport = (templateId: string) => {
    const template = reportTemplates.find(t => t.id === templateId);
    if (template) {
      console.log(`Generating report: ${template.name}`);
      // In real app, this would trigger backend report generation
    }
  };

  return (
    <DashboardLayout>
      <AnimatedBackground variant="minimal" className="opacity-10" />
      <div className="container mx-auto max-w-7xl relative z-10">
        {/* Header */}
        <div className="mb-8 animate-entrance">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <FileText className="h-8 w-8 text-blue-600" />
                Rapports & Exports 📋
              </h1>
              <p className="text-muted-foreground mt-1">
                Générez et gérez vos rapports personnalisés
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filtres
              </Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Export Groupé
              </Button>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="generated">Rapports Générés</TabsTrigger>
            <TabsTrigger value="templates">Modèles de Rapports</TabsTrigger>
            <TabsTrigger value="scheduled">Rapports Programmés</TabsTrigger>
          </TabsList>

          {/* Generated Reports Tab */}
          <TabsContent value="generated">
            <div className="space-y-6">
              {/* Search and Filters */}
              <AnimatedCard
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder="Rechercher dans les rapports..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="week">Cette semaine</SelectItem>
                      <SelectItem value="month">Ce mois</SelectItem>
                      <SelectItem value="quarter">Ce trimestre</SelectItem>
                      <SelectItem value="year">Cette année</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </AnimatedCard>

              {/* Reports List */}
              <div className="space-y-4">
                {generatedReports.map((report, index) => (
                  <AnimatedCard
                    key={report.id}
                    hoverEffect="lift"
                    entranceAnimation="slideUp"
                    delay={0.1 + index * 0.05}
                  >
                    <div className="flex items-center justify-between p-6">
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-blue-50 rounded-lg">
                          {getStatusIcon(report.status)}
                        </div>
                        <div>
                          <h3 className="font-semibold">{report.title}</h3>
                          <p className="text-sm text-muted-foreground">{report.type}</p>
                          <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                            <span>Par {report.generatedBy}</span>
                            <span>•</span>
                            <span>{report.generatedAt}</span>
                            <span>•</span>
                            <span>{report.size}</span>
                            <span>•</span>
                            <span>{report.downloads} téléchargements</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{report.format}</Badge>
                        {report.shared && (
                          <Badge className="bg-green-100 text-green-800">Partagé</Badge>
                        )}
                        {report.status === "generating" ? (
                          <div className="flex items-center gap-2">
                            <Progress value={65} className="w-20 h-2" />
                            <span className="text-sm text-muted-foreground">65%</span>
                          </div>
                        ) : (
                          <div className="flex gap-1">
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Share className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Mail className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  </AnimatedCard>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Templates Tab */}
          <TabsContent value="templates">
            <div className="space-y-6">
              {/* Categories Filter */}
              <AnimatedCard
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="flex flex-wrap gap-2">
                  {reportCategories.map((category) => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category.id)}
                      className="gap-2"
                    >
                      {category.name}
                      <Badge variant="secondary" className="ml-1">
                        {category.count}
                      </Badge>
                    </Button>
                  ))}
                </div>
              </AnimatedCard>

              {/* Templates Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {getFilteredTemplates().map((template, index) => (
                  <AnimatedCard
                    key={template.id}
                    hoverEffect="scale"
                    entranceAnimation="slideUp"
                    delay={0.1 + index * 0.1}
                  >
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="p-2 bg-blue-50 rounded-lg text-blue-600">
                          {template.icon}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold">{template.name}</h3>
                          <p className="text-sm text-muted-foreground">{template.description}</p>
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Temps estimé:</span>
                          <span className="font-medium">{template.estimatedTime}</span>
                        </div>
                        
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Dernière génération:</span>
                          <span className="font-medium">{template.lastGenerated}</span>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span className="text-muted-foreground">Popularité:</span>
                            <span className="font-medium">{template.popularity}%</span>
                          </div>
                          <Progress value={template.popularity} className="h-2" />
                        </div>
                      </div>
                      
                      <div className="flex gap-2 mt-4">
                        <Button 
                          className="flex-1"
                          onClick={() => generateReport(template.id)}
                        >
                          Générer
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </AnimatedCard>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Scheduled Reports Tab */}
          <TabsContent value="scheduled">
            <AnimatedCard
              title="Rapports Programmés"
              description="Automatisez la génération de vos rapports récurrents"
              hoverEffect="lift"
              entranceAnimation="fadeIn"
              delay={0.1}
            >
              <div className="text-center py-12">
                <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Programmation de Rapports</h3>
                <p className="text-muted-foreground mb-6">
                  Configurez des rapports automatiques pour recevoir vos données importantes régulièrement
                </p>
                <div className="flex gap-2 justify-center">
                  <Button>
                    <Calendar className="mr-2 h-4 w-4" />
                    Programmer un Rapport
                  </Button>
                  <Button variant="outline">
                    <FileText className="mr-2 h-4 w-4" />
                    Voir les Modèles
                  </Button>
                </div>
              </div>
            </AnimatedCard>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default AdminReports;
