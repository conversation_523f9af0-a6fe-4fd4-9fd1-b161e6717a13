import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

export const useGSAPAnimations = () => {
  // Page entrance animation
  const animatePageEntrance = (selector: string, delay: number = 0) => {
    gsap.fromTo(selector, 
      { 
        opacity: 0, 
        y: 30,
        scale: 0.95
      },
      { 
        opacity: 1, 
        y: 0,
        scale: 1,
        duration: 0.8,
        delay,
        ease: "power2.out",
        stagger: 0.1
      }
    );
  };

  // Card hover animations
  const animateCardHover = (element: HTMLElement) => {
    const tl = gsap.timeline({ paused: true });
    
    tl.to(element, {
      scale: 1.02,
      y: -5,
      boxShadow: "0 10px 30px rgba(0,0,0,0.15)",
      duration: 0.3,
      ease: "power2.out"
    });

    return {
      play: () => tl.play(),
      reverse: () => tl.reverse()
    };
  };

  // Success celebration animation
  const celebrateSuccess = (element: HTMLElement) => {
    // Confetti-like particles
    const particles = [];
    for (let i = 0; i < 12; i++) {
      const particle = document.createElement('div');
      particle.className = 'celebration-particle';
      particle.style.cssText = `
        position: absolute;
        width: 8px;
        height: 8px;
        background: hsl(${Math.random() * 360}, 70%, 60%);
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
      `;
      element.appendChild(particle);
      particles.push(particle);
    }

    // Animate main element
    gsap.to(element, {
      scale: 1.1,
      duration: 0.2,
      yoyo: true,
      repeat: 1,
      ease: "power2.inOut"
    });

    // Animate particles
    particles.forEach((particle, index) => {
      gsap.to(particle, {
        x: (Math.random() - 0.5) * 100,
        y: (Math.random() - 0.5) * 100,
        opacity: 0,
        scale: 0,
        duration: 1,
        delay: index * 0.05,
        ease: "power2.out",
        onComplete: () => particle.remove()
      });
    });
  };

  // Badge unlock animation
  const animateBadgeUnlock = (element: HTMLElement) => {
    const tl = gsap.timeline();
    
    // Initial state
    gsap.set(element, { scale: 0, rotation: -180, opacity: 0 });
    
    tl.to(element, {
      scale: 1.2,
      rotation: 0,
      opacity: 1,
      duration: 0.6,
      ease: "back.out(1.7)"
    })
    .to(element, {
      scale: 1,
      duration: 0.3,
      ease: "power2.out"
    })
    .to(element, {
      boxShadow: "0 0 20px rgba(255, 215, 0, 0.6)",
      duration: 0.5,
      yoyo: true,
      repeat: 3
    });

    return tl;
  };

  // Progress bar animation
  const animateProgressBar = (element: HTMLElement, progress: number) => {
    const progressBar = element.querySelector('[data-progress-fill]') as HTMLElement;
    if (progressBar) {
      gsap.fromTo(progressBar, 
        { width: "0%" },
        { 
          width: `${progress}%`,
          duration: 1.5,
          ease: "power2.out"
        }
      );
    }
  };

  // Number counter animation
  const animateCounter = (element: HTMLElement, from: number, to: number, duration: number = 2) => {
    const obj = { value: from };
    gsap.to(obj, {
      value: to,
      duration,
      ease: "power2.out",
      onUpdate: () => {
        element.textContent = Math.round(obj.value).toString();
      }
    });
  };

  // Floating animation for decorative elements
  const animateFloat = (selector: string) => {
    gsap.to(selector, {
      y: -10,
      duration: 2,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1,
      stagger: 0.2
    });
  };

  // Shake animation for errors
  const animateShake = (element: HTMLElement) => {
    gsap.to(element, {
      x: -5,
      duration: 0.1,
      yoyo: true,
      repeat: 5,
      ease: "power2.inOut",
      onComplete: () => gsap.set(element, { x: 0 })
    });
  };

  // Pulse animation for notifications
  const animatePulse = (element: HTMLElement) => {
    gsap.to(element, {
      scale: 1.05,
      duration: 0.5,
      yoyo: true,
      repeat: -1,
      ease: "power2.inOut"
    });
  };

  // Slide in from side animation
  const animateSlideIn = (element: HTMLElement, direction: 'left' | 'right' | 'top' | 'bottom' = 'left') => {
    const directions = {
      left: { x: -100, y: 0 },
      right: { x: 100, y: 0 },
      top: { x: 0, y: -100 },
      bottom: { x: 0, y: 100 }
    };

    const { x, y } = directions[direction];

    gsap.fromTo(element,
      { x, y, opacity: 0 },
      { 
        x: 0, 
        y: 0, 
        opacity: 1, 
        duration: 0.6, 
        ease: "power2.out" 
      }
    );
  };

  // Typewriter effect
  const animateTypewriter = (element: HTMLElement, text: string, speed: number = 0.05) => {
    element.textContent = '';
    const chars = text.split('');
    
    chars.forEach((char, index) => {
      gsap.to({}, {
        duration: speed,
        delay: index * speed,
        onComplete: () => {
          element.textContent += char;
        }
      });
    });
  };

  // Morphing background animation
  const animateMorphingBackground = (element: HTMLElement) => {
    const tl = gsap.timeline({ repeat: -1 });
    
    tl.to(element, {
      background: "linear-gradient(45deg, #667eea 0%, #764ba2 100%)",
      duration: 3,
      ease: "power2.inOut"
    })
    .to(element, {
      background: "linear-gradient(45deg, #f093fb 0%, #f5576c 100%)",
      duration: 3,
      ease: "power2.inOut"
    })
    .to(element, {
      background: "linear-gradient(45deg, #4facfe 0%, #00f2fe 100%)",
      duration: 3,
      ease: "power2.inOut"
    });

    return tl;
  };

  // Scroll-triggered animations
  const animateOnScroll = (selector: string, animation: object) => {
    gsap.fromTo(selector, 
      { opacity: 0, y: 50 },
      {
        ...animation,
        scrollTrigger: {
          trigger: selector,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      }
    );
  };

  return {
    animatePageEntrance,
    animateCardHover,
    celebrateSuccess,
    animateBadgeUnlock,
    animateProgressBar,
    animateCounter,
    animateFloat,
    animateShake,
    animatePulse,
    animateSlideIn,
    animateTypewriter,
    animateMorphingBackground,
    animateOnScroll
  };
};

// Hook for automatic page entrance animations
export const usePageEntrance = (dependencies: any[] = []) => {
  const { animatePageEntrance } = useGSAPAnimations();

  useEffect(() => {
    const timer = setTimeout(() => {
      animatePageEntrance('.animate-entrance');
    }, 100);

    return () => clearTimeout(timer);
  }, dependencies);
};

// Hook for card hover effects
export const useCardHover = () => {
  const { animateCardHover } = useGSAPAnimations();
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const animation = animateCardHover(element);

    const handleMouseEnter = () => animation.play();
    const handleMouseLeave = () => animation.reverse();

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return elementRef;
};

// Hook for success celebrations
export const useSuccessCelebration = () => {
  const { celebrateSuccess } = useGSAPAnimations();

  const celebrate = (element: HTMLElement) => {
    celebrateSuccess(element);
  };

  return celebrate;
};

// Hook for progress animations
export const useProgressAnimation = (progress: number, dependencies: any[] = []) => {
  const { animateProgressBar } = useGSAPAnimations();
  const progressRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (progressRef.current) {
      animateProgressBar(progressRef.current, progress);
    }
  }, [progress, ...dependencies]);

  return progressRef;
};

// Hook for counter animations
export const useCounterAnimation = (value: number, duration: number = 2) => {
  const { animateCounter } = useGSAPAnimations();
  const counterRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (counterRef.current) {
      const currentValue = parseInt(counterRef.current.textContent || '0');
      animateCounter(counterRef.current, currentValue, value, duration);
    }
  }, [value, duration]);

  return counterRef;
};
