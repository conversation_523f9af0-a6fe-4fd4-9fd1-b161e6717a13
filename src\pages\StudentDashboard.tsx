import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import AnimatedBackground from "@/components/animations/AnimatedBackground";
import AnimatedCard, { AnimatedStatsCard } from "@/components/animations/AnimatedCard";
import AnimatedProgress from "@/components/animations/AnimatedProgress";
import { usePageEntrance } from "@/hooks/useGSAPAnimations";
import { 
  BookOpen, 
  Calendar, 
  Trophy, 
  TrendingUp,
  Clock,
  Target,
  Users,
  MessageSquare,
  Star,
  CheckCircle,
  AlertCircle,
  Play,
  FileText,
  Award,
  Zap,
  Brain,
  Coffee,
  Lightbulb
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const StudentDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Add page entrance animations
  usePageEntrance([]);

  // Mock student data
  const studentStats = {
    currentGPA: 3.67,
    completedCourses: 12,
    totalCredits: 45,
    studyStreak: 7,
    totalStudyHours: 127,
    badgesEarned: 15,
    forumPosts: 23,
    rank: 15,
    totalStudents: 1247
  };

  const upcomingAssignments = [
    {
      id: "1",
      title: "Projet React Final",
      course: "Développement Web",
      dueDate: "2024-01-20",
      priority: "high",
      progress: 65,
      timeLeft: "3 jours"
    },
    {
      id: "2",
      title: "Analyse Mathématique - Chapitre 5",
      course: "Mathématiques",
      dueDate: "2024-01-22",
      priority: "medium",
      progress: 30,
      timeLeft: "5 jours"
    },
    {
      id: "3",
      title: "Essai sur l'Intelligence Artificielle",
      course: "Informatique",
      dueDate: "2024-01-25",
      priority: "low",
      progress: 10,
      timeLeft: "8 jours"
    }
  ];

  const recentAchievements = [
    {
      id: "1",
      title: "Streak Master",
      description: "7 jours d'étude consécutifs",
      icon: "🔥",
      rarity: "rare",
      points: 150,
      unlockedAt: "Il y a 2 heures"
    },
    {
      id: "2",
      title: "Forum Helper",
      description: "20 réponses utiles sur le forum",
      icon: "🤝",
      rarity: "common",
      points: 100,
      unlockedAt: "Hier"
    },
    {
      id: "3",
      title: "Perfect Score",
      description: "Note parfaite en mathématiques",
      icon: "⭐",
      rarity: "epic",
      points: 300,
      unlockedAt: "Il y a 3 jours"
    }
  ];

  const todaySchedule = [
    {
      id: "1",
      time: "09:00",
      title: "Cours de Mathématiques",
      teacher: "Prof. Martin",
      room: "Salle 201",
      type: "course"
    },
    {
      id: "2",
      time: "11:00",
      title: "Session d'étude - React",
      description: "Travail sur le projet final",
      type: "study"
    },
    {
      id: "3",
      time: "14:00",
      title: "TP Informatique",
      teacher: "Dr. Dubois",
      room: "Lab 3",
      type: "lab"
    },
    {
      id: "4",
      time: "16:00",
      title: "Club de Programmation",
      description: "Réunion hebdomadaire",
      type: "club"
    }
  ];

  const quickActions = [
    {
      title: "Commencer une session d'étude",
      description: "Démarrer le chronomètre Pomodoro",
      icon: <Play className="h-5 w-5" />,
      action: () => navigate("/dashboard/study-planner"),
      color: "bg-green-500"
    },
    {
      title: "Voir mes notes",
      description: "Consulter les dernières évaluations",
      icon: <FileText className="h-5 w-5" />,
      action: () => navigate("/dashboard/grades"),
      color: "bg-blue-500"
    },
    {
      title: "Forum d'aide",
      description: "Poser une question ou aider",
      icon: <MessageSquare className="h-5 w-5" />,
      action: () => navigate("/dashboard/forum"),
      color: "bg-purple-500"
    },
    {
      title: "Mes achievements",
      description: "Voir mes badges et progrès",
      icon: <Trophy className="h-5 w-5" />,
      action: () => navigate("/dashboard/achievements"),
      color: "bg-yellow-500"
    }
  ];

  const motivationalQuotes = [
    "L'éducation est l'arme la plus puissante pour changer le monde. - Nelson Mandela",
    "Le succès, c'est tomber sept fois et se relever huit. - Proverbe japonais",
    "L'apprentissage ne s'arrête jamais. Continuez à grandir ! 🌱",
    "Chaque expert était autrefois un débutant. Persévérez ! 💪"
  ];

  const [currentQuote] = useState(motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "text-red-600 bg-red-50 border-red-200";
      case "medium": return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "low": return "text-green-600 bg-green-50 border-green-200";
      default: return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "course": return <BookOpen className="h-4 w-4 text-blue-600" />;
      case "study": return <Brain className="h-4 w-4 text-green-600" />;
      case "lab": return <Lightbulb className="h-4 w-4 text-purple-600" />;
      case "club": return <Users className="h-4 w-4 text-orange-600" />;
      default: return <Calendar className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <DashboardLayout>
      <AnimatedBackground variant="waves" className="opacity-10" />
      <div className="container mx-auto max-w-7xl relative z-10">
        {/* Header */}
        <div className="mb-8 animate-entrance">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Coffee className="h-8 w-8 text-orange-600" />
                Bonjour, {user?.firstName} ! 🌟
              </h1>
              <p className="text-muted-foreground mt-1">
                Prêt pour une nouvelle journée d'apprentissage ? Voici votre tableau de bord personnalisé.
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Votre rang</p>
              <p className="text-2xl font-bold text-primary">#{studentStats.rank}</p>
              <p className="text-xs text-muted-foreground">sur {studentStats.totalStudents} étudiants</p>
            </div>
          </div>
        </div>

        {/* Motivational Quote */}
        <AnimatedCard
          className="mb-8 animate-entrance bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200"
          hoverEffect="glow"
          entranceAnimation="fadeIn"
          delay={0.1}
        >
          <div className="flex items-center gap-3 p-4">
            <div className="text-2xl">💡</div>
            <div>
              <p className="font-medium text-blue-900 italic">"{currentQuote}"</p>
            </div>
          </div>
        </AnimatedCard>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <AnimatedStatsCard
            icon={<TrendingUp className="h-6 w-6" />}
            title="GPA Actuel"
            value={studentStats.currentGPA}
            delay={0.1}
          />
          <AnimatedStatsCard
            icon={<Clock className="h-6 w-6" />}
            title="Heures d'Étude"
            value={studentStats.totalStudyHours}
            delay={0.2}
          />
          <AnimatedStatsCard
            icon={<Zap className="h-6 w-6" />}
            title="Streak d'Étude"
            value={studentStats.studyStreak}
            delay={0.3}
          />
          <AnimatedStatsCard
            icon={<Award className="h-6 w-6" />}
            title="Badges Gagnés"
            value={studentStats.badgesEarned}
            delay={0.4}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Quick Actions */}
            <AnimatedCard
              title="Actions Rapides"
              description="Accès direct à vos outils favoris"
              hoverEffect="lift"
              entranceAnimation="slideUp"
              delay={0.1}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <div
                    key={index}
                    onClick={action.action}
                    className="p-4 rounded-lg border cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-105"
                  >
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${action.color} text-white`}>
                        {action.icon}
                      </div>
                      <div>
                        <h4 className="font-medium">{action.title}</h4>
                        <p className="text-sm text-muted-foreground">{action.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </AnimatedCard>

            {/* Upcoming Assignments */}
            <AnimatedCard
              title="Devoirs à Venir"
              description="Vos prochaines échéances importantes"
              hoverEffect="glow"
              entranceAnimation="slideUp"
              delay={0.2}
            >
              <div className="space-y-4">
                {upcomingAssignments.map((assignment) => (
                  <div key={assignment.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-medium">{assignment.title}</h4>
                        <p className="text-sm text-muted-foreground">{assignment.course}</p>
                      </div>
                      <div className="text-right">
                        <Badge className={getPriorityColor(assignment.priority)}>
                          {assignment.priority}
                        </Badge>
                        <p className="text-xs text-muted-foreground mt-1">{assignment.timeLeft}</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Progression</span>
                        <span>{assignment.progress}%</span>
                      </div>
                      <AnimatedProgress 
                        value={assignment.progress} 
                        className="h-2"
                        color={assignment.priority === 'high' ? 'error' : assignment.priority === 'medium' ? 'warning' : 'success'}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </AnimatedCard>

            {/* Today's Schedule */}
            <AnimatedCard
              title="Planning d'Aujourd'hui"
              description="Votre emploi du temps pour la journée"
              hoverEffect="tilt"
              entranceAnimation="slideUp"
              delay={0.3}
            >
              <div className="space-y-3">
                {todaySchedule.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 p-3 bg-muted/50 rounded-lg">
                    <div className="text-center min-w-[60px]">
                      <p className="font-medium text-sm">{item.time}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getTypeIcon(item.type)}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{item.title}</h4>
                      <p className="text-xs text-muted-foreground">
                        {item.teacher && `${item.teacher} • `}
                        {item.room || item.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </AnimatedCard>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Recent Achievements */}
            <AnimatedCard
              title="Derniers Succès"
              description="Vos récents accomplissements"
              hoverEffect="scale"
              entranceAnimation="slideLeft"
              delay={0.2}
            >
              <div className="space-y-3">
                {recentAchievements.map((achievement) => (
                  <div key={achievement.id} className="p-3 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">{achievement.icon}</span>
                      <div>
                        <h4 className="font-medium text-sm">{achievement.title}</h4>
                        <p className="text-xs text-muted-foreground">{achievement.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                        +{achievement.points} XP
                      </Badge>
                      <p className="text-xs text-muted-foreground">{achievement.unlockedAt}</p>
                    </div>
                  </div>
                ))}
              </div>
              <Button 
                variant="outline" 
                className="w-full mt-4"
                onClick={() => navigate("/dashboard/achievements")}
              >
                <Trophy className="mr-2 h-4 w-4" />
                Voir Tous les Badges
              </Button>
            </AnimatedCard>

            {/* Study Progress */}
            <AnimatedCard
              title="Progression d'Étude"
              description="Votre avancement cette semaine"
              hoverEffect="glow"
              entranceAnimation="slideLeft"
              delay={0.3}
            >
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-primary">{studentStats.studyStreak}</div>
                  <p className="text-sm text-muted-foreground">jours consécutifs</p>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Objectif hebdomadaire</span>
                    <span>25h / 30h</span>
                  </div>
                  <Progress value={83} className="h-2" />
                  
                  <div className="flex justify-between text-sm">
                    <span>Cours complétés</span>
                    <span>{studentStats.completedCourses}</span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span>Crédits obtenus</span>
                    <span>{studentStats.totalCredits}</span>
                  </div>
                </div>
              </div>
            </AnimatedCard>

            {/* Quick Stats */}
            <AnimatedCard
              title="Statistiques Rapides"
              hoverEffect="lift"
              entranceAnimation="slideLeft"
              delay={0.4}
            >
              <div className="grid grid-cols-2 gap-3">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <MessageSquare className="h-5 w-5 text-blue-600 mx-auto mb-1" />
                  <p className="text-lg font-bold text-blue-600">{studentStats.forumPosts}</p>
                  <p className="text-xs text-blue-600">Posts Forum</p>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600 mx-auto mb-1" />
                  <p className="text-lg font-bold text-green-600">98%</p>
                  <p className="text-xs text-green-600">Assiduité</p>
                </div>
              </div>
            </AnimatedCard>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default StudentDashboard;
