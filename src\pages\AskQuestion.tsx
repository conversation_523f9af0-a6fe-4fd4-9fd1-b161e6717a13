import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { 
  Send, 
  Tag, 
  BookOpen, 
  AlertCircle, 
  Upload, 
  X,
  Plus,
  HelpCircle
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";

const AskQuestion = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    category: "",
    course: "",
    tags: [] as string[],
    priority: "normal",
    anonymous: false
  });
  
  const [newTag, setNewTag] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Mock data for categories and courses
  const categories = [
    { id: "general", name: "General Discussion", icon: "💬" },
    { id: "homework", name: "Homework Help", icon: "📝" },
    { id: "technical", name: "Technical Issues", icon: "🔧" },
    { id: "career", name: "Career Advice", icon: "💼" },
    { id: "study-tips", name: "Study Tips", icon: "📚" },
    { id: "projects", name: "Project Help", icon: "🚀" }
  ];

  const courses = [
    { id: "cs101", name: "CS 101 - Introduction to Computer Science" },
    { id: "math242", name: "MATH 242 - Calculus II" },
    { id: "eng210", name: "ENG 210 - Technical Writing" },
    { id: "cs201", name: "CS 201 - Data Structures and Algorithms" },
    { id: "phys101", name: "PHYS 101 - General Physics" }
  ];

  const suggestedTags = [
    "javascript", "react", "python", "java", "css", "html", "database", 
    "algorithm", "debugging", "assignment", "exam", "project", "homework",
    "calculus", "physics", "writing", "research", "group-work"
  ];

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    } else if (formData.title.length < 10) {
      newErrors.title = "Title must be at least 10 characters long";
    }

    if (!formData.content.trim()) {
      newErrors.content = "Question content is required";
    } else if (formData.content.length < 20) {
      newErrors.content = "Question content must be at least 20 characters long";
    }

    if (!formData.category) {
      newErrors.category = "Please select a category";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error("Please fix the errors before submitting");
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success("Question posted successfully!");
      navigate("/dashboard/forum");
    } catch (error) {
      toast.error("Failed to post question. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim().toLowerCase())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim().toLowerCase()]
      }));
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addSuggestedTag = (tag: string) => {
    if (!formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-4xl">
        <div className="mb-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <HelpCircle className="h-6 w-6" />
            Ask a Question
          </h1>
          <p className="text-muted-foreground">
            Get help from your classmates and instructors by asking a detailed question
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Your Question</CardTitle>
                <CardDescription>
                  Be specific and clear to get the best answers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Title */}
                  <div className="space-y-2">
                    <Label htmlFor="title">Question Title *</Label>
                    <Input
                      id="title"
                      placeholder="What's your programming question? Be specific."
                      value={formData.title}
                      onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                      className={errors.title ? "border-red-500" : ""}
                    />
                    {errors.title && (
                      <p className="text-sm text-red-600">{errors.title}</p>
                    )}
                  </div>

                  {/* Category and Course */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">Category *</Label>
                      <Select 
                        value={formData.category} 
                        onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                      >
                        <SelectTrigger className={errors.category ? "border-red-500" : ""}>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              <span className="flex items-center gap-2">
                                <span>{category.icon}</span>
                                {category.name}
                              </span>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.category && (
                        <p className="text-sm text-red-600">{errors.category}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="course">Related Course (Optional)</Label>
                      <Select 
                        value={formData.course} 
                        onValueChange={(value) => setFormData(prev => ({ ...prev, course: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select a course" />
                        </SelectTrigger>
                        <SelectContent>
                          {courses.map((course) => (
                            <SelectItem key={course.id} value={course.id}>
                              {course.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="space-y-2">
                    <Label htmlFor="content">Question Details *</Label>
                    <Textarea
                      id="content"
                      placeholder="Describe your question in detail. Include what you've tried, what you expected, and what actually happened..."
                      value={formData.content}
                      onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                      className={`min-h-32 ${errors.content ? "border-red-500" : ""}`}
                    />
                    {errors.content && (
                      <p className="text-sm text-red-600">{errors.content}</p>
                    )}
                    <p className="text-sm text-muted-foreground">
                      {formData.content.length}/500 characters minimum: 20
                    </p>
                  </div>

                  {/* Tags */}
                  <div className="space-y-2">
                    <Label>Tags (Optional)</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Add a tag..."
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
                        className="flex-1"
                      />
                      <Button type="button" onClick={addTag} variant="outline">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    {/* Current Tags */}
                    {formData.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {formData.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            {tag}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeTag(tag)}
                              className="h-4 w-4 p-0 hover:bg-transparent"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Suggested Tags */}
                    <div className="space-y-2">
                      <p className="text-sm text-muted-foreground">Suggested tags:</p>
                      <div className="flex flex-wrap gap-2">
                        {suggestedTags
                          .filter(tag => !formData.tags.includes(tag))
                          .slice(0, 8)
                          .map((tag) => (
                            <Button
                              key={tag}
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => addSuggestedTag(tag)}
                              className="text-xs"
                            >
                              {tag}
                            </Button>
                          ))}
                      </div>
                    </div>
                  </div>

                  {/* Priority */}
                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority</Label>
                    <Select 
                      value={formData.priority} 
                      onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low - General question</SelectItem>
                        <SelectItem value="normal">Normal - Need help soon</SelectItem>
                        <SelectItem value="high">High - Urgent assignment help</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Submit Buttons */}
                  <div className="flex items-center justify-between pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate("/dashboard/forum")}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Posting...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" />
                          Post Question
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Tips Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Writing Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">📝 Good Question Title</h4>
                  <p className="text-sm text-muted-foreground">
                    "How to fix 'undefined' error in React useState hook?"
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">❌ Avoid</h4>
                  <p className="text-sm text-muted-foreground">
                    "Help me with my code" or "This doesn't work"
                  </p>
                </div>
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">💡 Include</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• What you're trying to do</li>
                    <li>• What you've tried</li>
                    <li>• Error messages</li>
                    <li>• Expected vs actual results</li>
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Guidelines */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Community Guidelines:</strong> Be respectful, search before posting, 
                and provide enough detail for others to help you effectively.
              </AlertDescription>
            </Alert>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Questions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-sm">
                    <p className="font-medium">React state not updating</p>
                    <p className="text-muted-foreground">2 answers • 5 min ago</p>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Calculus integration help</p>
                    <p className="text-muted-foreground">1 answer • 15 min ago</p>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Database design question</p>
                    <p className="text-muted-foreground">3 answers • 1 hour ago</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AskQuestion;
