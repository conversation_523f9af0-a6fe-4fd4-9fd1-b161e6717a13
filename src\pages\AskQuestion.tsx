import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useForum, useTags } from "@/hooks/useForum";
import DashboardLayout from "@/components/layout/DashboardLayout";
import {
  Send,
  Tag,
  BookOpen,
  AlertCircle,
  Upload,
  X,
  Plus,
  HelpCircle,
  ArrowLeft,
  Loader2,
  CheckCircle,
  Lightbulb
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

const AskQuestion = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { createQuestion } = useForum();
  const { popularTags } = useTags();

  const [formData, setFormData] = useState({
    title: "",
    content: "",
    tags: [] as string[]
  });

  const [newTag, setNewTag] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});



  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.title.trim()) {
      newErrors.title = "Le titre est requis";
    } else if (formData.title.length < 10) {
      newErrors.title = "Le titre doit contenir au moins 10 caractères";
    }

    if (!formData.content.trim()) {
      newErrors.content = "Le contenu est requis";
    } else if (formData.content.length < 20) {
      newErrors.content = "Le contenu doit contenir au moins 20 caractères";
    }

    if (formData.tags.length === 0) {
      newErrors.tags = "Au moins un tag est requis";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Veuillez corriger les erreurs dans le formulaire");
      return;
    }

    setIsSubmitting(true);

    try {
      const question = await createQuestion(formData);
      toast.success("Question créée avec succès!");
      navigate(`/dashboard/forum/question/${question.id}`);
    } catch (error: any) {
      toast.error(error.message || "Erreur lors de la création de la question");
    } finally {
      setIsSubmitting(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim().toLowerCase())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim().toLowerCase()]
      }));
      setNewTag("");
      setErrors(prev => ({ ...prev, tags: "" }));
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addPopularTag = (tag: string) => {
    if (!formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
      setErrors(prev => ({ ...prev, tags: "" }));
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate("/dashboard/forum")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour au Forum
          </Button>

          <div className="flex items-center gap-3 mb-2">
            <HelpCircle className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold">Poser une Question</h1>
          </div>
          <p className="text-muted-foreground">
            Partagez votre question avec la communauté et obtenez de l'aide
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Détails de votre question</CardTitle>
                <CardDescription>
                  Soyez précis et détaillé pour obtenir les meilleures réponses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Title */}
                  <div className="space-y-2">
                    <Label htmlFor="title">Titre de la question *</Label>
                    <Input
                      id="title"
                      placeholder="Ex: Comment calculer les dérivées partielles en mathématiques ?"
                      value={formData.title}
                      onChange={(e) => {
                        setFormData(prev => ({ ...prev, title: e.target.value }));
                        setErrors(prev => ({ ...prev, title: "" }));
                      }}
                      className={errors.title ? "border-red-500" : ""}
                    />
                    {errors.title && (
                      <p className="text-sm text-red-500">{errors.title}</p>
                    )}
                  </div>

                  {/* Content */}
                  <div className="space-y-2">
                    <Label htmlFor="content">Description détaillée *</Label>
                    <Textarea
                      id="content"
                      placeholder="Décrivez votre problème en détail. Incluez ce que vous avez déjà essayé et où vous êtes bloqué..."
                      value={formData.content}
                      onChange={(e) => {
                        setFormData(prev => ({ ...prev, content: e.target.value }));
                        setErrors(prev => ({ ...prev, content: "" }));
                      }}
                      className={`min-h-[150px] ${errors.content ? "border-red-500" : ""}`}
                    />
                    {errors.content && (
                      <p className="text-sm text-red-500">{errors.content}</p>
                    )}
                  </div>

                  {/* Tags */}
                  <div className="space-y-2">
                    <Label>Tags *</Label>
                    <div className="flex gap-2">
                      <Input
                        placeholder="Ajouter un tag..."
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className={errors.tags ? "border-red-500" : ""}
                      />
                      <Button type="button" onClick={addTag} variant="outline">
                        Ajouter
                      </Button>
                    </div>

                    {/* Selected Tags */}
                    {formData.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {formData.tags.map((tag) => (
                          <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                            <Tag className="h-3 w-3" />
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="ml-1 hover:text-red-500"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {errors.tags && (
                      <p className="text-sm text-red-500">{errors.tags}</p>
                    )}
                  </div>

                  <Separator />

                  {/* Submit Button */}
                  <div className="flex gap-3">
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="flex-1"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Publication...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Publier la Question
                        </>
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => navigate("/dashboard/forum")}
                    >
                      Annuler
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  Conseils pour une bonne question
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  <p>• Soyez précis dans votre titre</p>
                  <p>• Décrivez le contexte et ce que vous avez essayé</p>
                  <p>• Utilisez des tags pertinents</p>
                  <p>• Incluez des exemples si possible</p>
                  <p>• Relisez avant de publier</p>
                </div>
              </CardContent>
            </Card>

            {/* Popular Tags */}
            {popularTags.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Tag className="h-5 w-5 text-blue-500" />
                    Tags Populaires
                  </CardTitle>
                  <CardDescription>
                    Cliquez pour ajouter à votre question
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {popularTags.slice(0, 10).map((tag) => (
                      <Badge
                        key={tag.id}
                        variant="outline"
                        className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                        onClick={() => addPopularTag(tag.name)}
                      >
                        {tag.name} ({tag.question_count})
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Guidelines */}
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Règles de la communauté :</strong> Soyez respectueux, recherchez avant de poster,
                et fournissez suffisamment de détails pour que les autres puissent vous aider efficacement.
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AskQuestion;
