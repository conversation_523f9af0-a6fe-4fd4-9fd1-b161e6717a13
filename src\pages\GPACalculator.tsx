import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { 
  Calculator, 
  Plus, 
  Trash2, 
  Target, 
  TrendingUp,
  BookOpen,
  Award,
  BarChart3,
  Save,
  Download,
  Upload
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

interface Course {
  id: string;
  name: string;
  credits: number;
  grade: string;
  gradePoints: number;
  semester: string;
  isCompleted: boolean;
}

interface GPAGoal {
  targetGPA: number;
  currentGPA: number;
  remainingCredits: number;
  requiredGPA: number;
}

const GPACalculator = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("calculator");

  // Grade scale mapping
  const gradeScale = {
    "A+": 4.0, "A": 4.0, "A-": 3.7,
    "B+": 3.3, "B": 3.0, "B-": 2.7,
    "C+": 2.3, "C": 2.0, "C-": 1.7,
    "D+": 1.3, "D": 1.0, "F": 0.0
  };

  // Current courses state
  const [courses, setCourses] = useState<Course[]>([
    {
      id: "1",
      name: "Computer Science 101",
      credits: 3,
      grade: "A",
      gradePoints: 4.0,
      semester: "Fall 2024",
      isCompleted: true
    },
    {
      id: "2",
      name: "Calculus II",
      credits: 4,
      grade: "B+",
      gradePoints: 3.3,
      semester: "Fall 2024",
      isCompleted: true
    },
    {
      id: "3",
      name: "Physics 101",
      credits: 3,
      grade: "B",
      gradePoints: 3.0,
      semester: "Spring 2025",
      isCompleted: false
    }
  ]);

  // GPA goal state
  const [gpaGoal, setGpaGoal] = useState<GPAGoal>({
    targetGPA: 3.5,
    currentGPA: 0,
    remainingCredits: 60,
    requiredGPA: 0
  });

  // New course form state
  const [newCourse, setNewCourse] = useState({
    name: "",
    credits: 3,
    grade: "",
    semester: "Spring 2025"
  });

  const addCourse = () => {
    if (!newCourse.name || !newCourse.grade) {
      toast.error("Please fill in all course details");
      return;
    }

    const course: Course = {
      id: Date.now().toString(),
      name: newCourse.name,
      credits: newCourse.credits,
      grade: newCourse.grade,
      gradePoints: gradeScale[newCourse.grade as keyof typeof gradeScale],
      semester: newCourse.semester,
      isCompleted: true
    };

    setCourses(prev => [...prev, course]);
    setNewCourse({ name: "", credits: 3, grade: "", semester: "Spring 2025" });
    toast.success("Course added successfully!");
  };

  const removeCourse = (courseId: string) => {
    setCourses(prev => prev.filter(course => course.id !== courseId));
    toast.success("Course removed");
  };

  const updateCourse = (courseId: string, field: string, value: any) => {
    setCourses(prev => prev.map(course => {
      if (course.id === courseId) {
        const updated = { ...course, [field]: value };
        if (field === "grade") {
          updated.gradePoints = gradeScale[value as keyof typeof gradeScale];
        }
        return updated;
      }
      return course;
    }));
  };

  const calculateGPA = (courseList: Course[] = courses) => {
    const completedCourses = courseList.filter(course => course.isCompleted);
    if (completedCourses.length === 0) return 0;

    const totalPoints = completedCourses.reduce((sum, course) => 
      sum + (course.gradePoints * course.credits), 0
    );
    const totalCredits = completedCourses.reduce((sum, course) => 
      sum + course.credits, 0
    );

    return totalCredits > 0 ? totalPoints / totalCredits : 0;
  };

  const calculateSemesterGPA = (semester: string) => {
    const semesterCourses = courses.filter(course => 
      course.semester === semester && course.isCompleted
    );
    return calculateGPA(semesterCourses);
  };

  const getTotalCredits = () => {
    return courses.filter(course => course.isCompleted)
      .reduce((sum, course) => sum + course.credits, 0);
  };

  const calculateRequiredGPA = () => {
    const currentGPA = calculateGPA();
    const currentCredits = getTotalCredits();
    const targetGPA = gpaGoal.targetGPA;
    const remainingCredits = gpaGoal.remainingCredits;

    if (remainingCredits <= 0) return 0;

    const requiredTotalPoints = targetGPA * (currentCredits + remainingCredits);
    const currentTotalPoints = currentGPA * currentCredits;
    const requiredPoints = requiredTotalPoints - currentTotalPoints;

    return requiredPoints / remainingCredits;
  };

  const getGPAColor = (gpa: number) => {
    if (gpa >= 3.7) return "text-green-600";
    if (gpa >= 3.0) return "text-blue-600";
    if (gpa >= 2.0) return "text-yellow-600";
    return "text-red-600";
  };

  const getGradeDistribution = () => {
    const distribution: { [key: string]: number } = {};
    courses.filter(course => course.isCompleted).forEach(course => {
      distribution[course.grade] = (distribution[course.grade] || 0) + 1;
    });
    return distribution;
  };

  const getSemesters = () => {
    return [...new Set(courses.map(course => course.semester))];
  };

  const currentGPA = calculateGPA();
  const requiredGPA = calculateRequiredGPA();
  const gradeDistribution = getGradeDistribution();

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-6xl">
        <div className="mb-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Calculator className="h-6 w-6" />
            GPA Calculator
          </h1>
          <p className="text-muted-foreground">
            Calculate your GPA, track your progress, and plan for your academic goals
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="calculator">Calculator</TabsTrigger>
            <TabsTrigger value="goals">Goals</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          {/* Calculator Tab */}
          <TabsContent value="calculator" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Course Input */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Add Course</CardTitle>
                    <CardDescription>Enter your course details to calculate GPA</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                      <div className="md:col-span-2">
                        <Label htmlFor="courseName">Course Name</Label>
                        <Input
                          id="courseName"
                          placeholder="e.g., Computer Science 101"
                          value={newCourse.name}
                          onChange={(e) => setNewCourse(prev => ({ ...prev, name: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="credits">Credits</Label>
                        <Input
                          id="credits"
                          type="number"
                          min="1"
                          max="6"
                          value={newCourse.credits}
                          onChange={(e) => setNewCourse(prev => ({ ...prev, credits: parseInt(e.target.value) || 3 }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="grade">Grade</Label>
                        <Select value={newCourse.grade} onValueChange={(value) => setNewCourse(prev => ({ ...prev, grade: value }))}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select grade" />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.keys(gradeScale).map(grade => (
                              <SelectItem key={grade} value={grade}>
                                {grade} ({gradeScale[grade as keyof typeof gradeScale]})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="flex justify-between items-center mt-4">
                      <Select value={newCourse.semester} onValueChange={(value) => setNewCourse(prev => ({ ...prev, semester: value }))}>
                        <SelectTrigger className="w-48">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Fall 2024">Fall 2024</SelectItem>
                          <SelectItem value="Spring 2025">Spring 2025</SelectItem>
                          <SelectItem value="Summer 2025">Summer 2025</SelectItem>
                          <SelectItem value="Fall 2025">Fall 2025</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button onClick={addCourse}>
                        <Plus className="mr-2 h-4 w-4" />
                        Add Course
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Course List */}
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Your Courses</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {courses.map((course) => (
                        <div key={course.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-2">
                            <Input
                              value={course.name}
                              onChange={(e) => updateCourse(course.id, "name", e.target.value)}
                              className="font-medium"
                            />
                            <Input
                              type="number"
                              min="1"
                              max="6"
                              value={course.credits}
                              onChange={(e) => updateCourse(course.id, "credits", parseInt(e.target.value) || 3)}
                            />
                            <Select value={course.grade} onValueChange={(value) => updateCourse(course.id, "grade", value)}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {Object.keys(gradeScale).map(grade => (
                                  <SelectItem key={grade} value={grade}>
                                    {grade} ({gradeScale[grade as keyof typeof gradeScale]})
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{course.semester}</Badge>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeCourse(course.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* GPA Summary */}
              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>GPA Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-center">
                        <p className="text-sm text-muted-foreground">Current GPA</p>
                        <p className={`text-3xl font-bold ${getGPAColor(currentGPA)}`}>
                          {currentGPA.toFixed(2)}
                        </p>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Total Credits</span>
                          <span className="font-medium">{getTotalCredits()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Courses Completed</span>
                          <span className="font-medium">{courses.filter(c => c.isCompleted).length}</span>
                        </div>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <h4 className="font-medium">Semester GPAs</h4>
                        {getSemesters().map(semester => (
                          <div key={semester} className="flex justify-between text-sm">
                            <span>{semester}</span>
                            <span className={getGPAColor(calculateSemesterGPA(semester))}>
                              {calculateSemesterGPA(semester).toFixed(2)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="mt-4">
                  <CardHeader>
                    <CardTitle className="text-lg">Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Button variant="outline" className="w-full" size="sm">
                        <Save className="mr-2 h-4 w-4" />
                        Save Calculation
                      </Button>
                      <Button variant="outline" className="w-full" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Export PDF
                      </Button>
                      <Button variant="outline" className="w-full" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        Import Transcript
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Goals Tab */}
          <TabsContent value="goals" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    GPA Goal Planning
                  </CardTitle>
                  <CardDescription>
                    Set your target GPA and see what you need to achieve it
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="targetGPA">Target GPA</Label>
                      <Input
                        id="targetGPA"
                        type="number"
                        min="0"
                        max="4"
                        step="0.1"
                        value={gpaGoal.targetGPA}
                        onChange={(e) => setGpaGoal(prev => ({ ...prev, targetGPA: parseFloat(e.target.value) || 0 }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="remainingCredits">Remaining Credits</Label>
                      <Input
                        id="remainingCredits"
                        type="number"
                        min="0"
                        value={gpaGoal.remainingCredits}
                        onChange={(e) => setGpaGoal(prev => ({ ...prev, remainingCredits: parseInt(e.target.value) || 0 }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Goal Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Current GPA</span>
                      <span className={`font-bold ${getGPAColor(currentGPA)}`}>
                        {currentGPA.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Target GPA</span>
                      <span className="font-bold">{gpaGoal.targetGPA.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Required GPA</span>
                      <span className={`font-bold ${requiredGPA > 4.0 ? 'text-red-600' : getGPAColor(requiredGPA)}`}>
                        {requiredGPA.toFixed(2)}
                      </span>
                    </div>

                    <Progress 
                      value={Math.min((currentGPA / gpaGoal.targetGPA) * 100, 100)} 
                      className="mt-4"
                    />

                    {requiredGPA > 4.0 ? (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-800">
                          ⚠️ Your target GPA is not achievable with the remaining credits. 
                          Consider adjusting your target or taking more credits.
                        </p>
                      </div>
                    ) : requiredGPA > 3.7 ? (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <p className="text-sm text-yellow-800">
                          📈 You'll need to maintain excellent grades (mostly A's) to reach your target.
                        </p>
                      </div>
                    ) : (
                      <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                        <p className="text-sm text-green-800">
                          ✅ Your target GPA is achievable with consistent effort!
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Grade Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(gradeDistribution).map(([grade, count]) => (
                      <div key={grade} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{grade}</span>
                          <span className="text-sm text-muted-foreground">
                            ({gradeScale[grade as keyof typeof gradeScale]} points)
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="w-20 bg-muted rounded-full h-2">
                            <div 
                              className="bg-primary h-2 rounded-full"
                              style={{ width: `${(count / courses.filter(c => c.isCompleted).length) * 100}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium">{count}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Performance Insights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <Award className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-800">Academic Standing</span>
                      </div>
                      <p className="text-sm text-blue-700">
                        {currentGPA >= 3.5 ? "Dean's List" : 
                         currentGPA >= 3.0 ? "Good Standing" : 
                         currentGPA >= 2.0 ? "Satisfactory" : "Academic Warning"}
                      </p>
                    </div>

                    <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <BookOpen className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-green-800">Strongest Subject</span>
                      </div>
                      <p className="text-sm text-green-700">
                        {courses.reduce((best, course) => 
                          course.gradePoints > best.gradePoints ? course : best
                        ).name}
                      </p>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Quick Stats</h4>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">Avg Credits/Course:</span>
                          <span className="font-medium ml-1">
                            {(getTotalCredits() / courses.filter(c => c.isCompleted).length).toFixed(1)}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Total Quality Points:</span>
                          <span className="font-medium ml-1">
                            {(currentGPA * getTotalCredits()).toFixed(1)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Academic History</CardTitle>
                <CardDescription>Your complete academic record by semester</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {getSemesters().map(semester => (
                    <div key={semester}>
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-semibold">{semester}</h3>
                        <Badge variant="outline">
                          GPA: {calculateSemesterGPA(semester).toFixed(2)}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        {courses.filter(course => course.semester === semester).map(course => (
                          <div key={course.id} className="flex items-center justify-between p-2 border rounded">
                            <div>
                              <span className="font-medium">{course.name}</span>
                              <span className="text-sm text-muted-foreground ml-2">
                                ({course.credits} credits)
                              </span>
                            </div>
                            <Badge className={getGPAColor(course.gradePoints)}>
                              {course.grade}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default GPACalculator;
