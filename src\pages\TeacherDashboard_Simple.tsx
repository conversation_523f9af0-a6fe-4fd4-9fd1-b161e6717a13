import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  BookOpen, 
  Users, 
  Calendar, 
  FileText,
  MessageSquare,
  TrendingUp,
  Clock,
  Star,
  CheckCircle,
  AlertCircle,
  Plus,
  Edit
} from "lucide-react";

const TeacherDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Mock data
  const stats = {
    totalStudents: 156,
    activeCourses: 4,
    pendingGrading: 23,
    avgRating: 4.7
  };

  const courses = [
    {
      id: 1,
      code: "CS 101",
      name: "Introduction to Computer Science",
      students: 30,
      nextClass: "2025-01-16 10:00",
      room: "Tech 305",
      progress: 68
    },
    {
      id: 2,
      code: "CS 201",
      name: "Data Structures and Algorithms",
      students: 25,
      nextClass: "2025-01-16 14:00",
      room: "Tech 402",
      progress: 45
    },
    {
      id: 3,
      code: "CS 301",
      name: "Software Engineering",
      students: 22,
      nextClass: "2025-01-17 09:00",
      room: "Tech 501",
      progress: 72
    }
  ];

  const students = [
    { name: "Marie Dupont", course: "CS 101", grade: 18.5, trend: "up" },
    { name: "Jean Martin", course: "CS 201", grade: 16.8, trend: "stable" },
    { name: "Sophie Chen", course: "CS 301", grade: 19.2, trend: "up" },
    { name: "Alex Johnson", course: "CS 101", grade: 15.3, trend: "down" },
    { name: "Emma Wilson", course: "CS 201", grade: 17.9, trend: "up" }
  ];

  const todaySchedule = [
    {
      time: "09:00 - 10:30",
      course: "CS 101",
      type: "Cours Magistral",
      room: "Tech 305",
      students: 30
    },
    {
      time: "11:00 - 12:00",
      course: "CS 201",
      type: "Consultation",
      room: "Bureau 204",
      students: 5
    },
    {
      time: "14:00 - 16:00",
      course: "CS 301",
      type: "Travaux Pratiques",
      room: "Lab 101",
      students: 22
    }
  ];

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up": return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "down": return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />;
      default: return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Espace Enseignant 👨‍🏫
            </h1>
            <p className="text-muted-foreground mt-1">
              Bonjour Prof. {user?.firstName}, gérez vos cours et étudiants.
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex gap-2">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Créer Devoir
            </Button>
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Noter
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Étudiants</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stats.totalStudents}</div>
              <p className="text-xs text-muted-foreground">Tous cours confondus</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cours Actifs</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stats.activeCourses}</div>
              <p className="text-xs text-muted-foreground">Ce semestre</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">À Noter</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stats.pendingGrading}</div>
              <p className="text-xs text-muted-foreground">Devoirs en attente</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Évaluation</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stats.avgRating}/5</div>
              <p className="text-xs text-muted-foreground">Note moyenne</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Actions Rapides
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => navigate('/dashboard/assignments')}
              >
                <Plus className="h-6 w-6" />
                <span className="text-sm">Créer Devoir</span>
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => navigate('/dashboard/assignments')}
              >
                <FileText className="h-6 w-6" />
                <span className="text-sm">Noter</span>
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => navigate('/dashboard/forum')}
              >
                <MessageSquare className="h-6 w-6" />
                <span className="text-sm">Forum Q&A</span>
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => navigate('/dashboard/calendar')}
              >
                <Calendar className="h-6 w-6" />
                <span className="text-sm">Planning</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="courses">Mes Cours</TabsTrigger>
            <TabsTrigger value="students">Étudiants</TabsTrigger>
            <TabsTrigger value="schedule">Planning</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Course Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>Performance des Cours</CardTitle>
                  <CardDescription>Progression et statistiques</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {courses.map((course) => (
                    <div key={course.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-medium text-foreground">{course.code}</h4>
                          <p className="text-sm text-muted-foreground">{course.name}</p>
                        </div>
                        <Badge variant="outline">{course.students} étudiants</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Progression: {course.progress}%</span>
                        <Button size="sm" variant="outline">Voir Détails</Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Top Students */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Étudiants</CardTitle>
                  <CardDescription>Meilleures performances récentes</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {students.map((student, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-primary">{student.name.charAt(0)}</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-foreground">{student.name}</h4>
                          <p className="text-sm text-muted-foreground">{student.course}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-foreground">{student.grade}/20</span>
                        {getTrendIcon(student.trend)}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="courses" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {courses.map((course) => (
                <Card key={course.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{course.code}</CardTitle>
                        <CardDescription>{course.name}</CardDescription>
                      </div>
                      <Badge>{course.students} étudiants</Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Prochain cours</span>
                        <span className="font-medium text-foreground">
                          {new Date(course.nextClass).toLocaleDateString('fr-FR')}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Salle</span>
                        <span className="font-medium text-foreground">{course.room}</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" className="flex-1">Gérer</Button>
                      <Button size="sm" variant="outline">Voir</Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="students" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Gestion des Étudiants</CardTitle>
                <CardDescription>Vue d'ensemble de tous vos étudiants</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {students.map((student, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="font-medium text-primary">{student.name.charAt(0)}</span>
                        </div>
                        <div>
                          <h4 className="font-medium text-foreground">{student.name}</h4>
                          <p className="text-sm text-muted-foreground">{student.course}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <div className="font-medium text-foreground">{student.grade}/20</div>
                          <div className="flex items-center gap-1">
                            {getTrendIcon(student.trend)}
                            <span className="text-xs text-muted-foreground">Tendance</span>
                          </div>
                        </div>
                        <Button size="sm" variant="outline">Profil</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Planning du Jour
                </CardTitle>
                <CardDescription>
                  {new Date().toLocaleDateString('fr-FR', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {todaySchedule.map((item, index) => (
                  <div key={index} className="flex items-center gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="text-center min-w-[100px]">
                      <div className="font-semibold text-foreground">{item.time}</div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium text-foreground">{item.course}</h4>
                      <p className="text-sm text-muted-foreground">{item.type} • {item.room}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline">{item.students} étudiants</Badge>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default TeacherDashboard;
