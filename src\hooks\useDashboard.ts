import { useState, useEffect } from 'react';
import { apiService } from '@/services/api';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export interface DashboardStats {
  // User stats
  total_points?: number;
  study_streak?: number;
  questions_asked?: number;
  questions_answered?: number;
  best_answer_count?: number;
  
  // Forum stats
  total_questions?: number;
  total_answers?: number;
  solved_questions?: number;
  active_users?: number;
  popular_tags?: Array<{ name: string; question_count: number }>;
  recent_activity?: Array<any>;
  
  // Admin stats (if admin)
  total_users?: number;
  new_users_today?: number;
  students_count?: number;
  teachers_count?: number;
  admins_count?: number;
}

export const useDashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [forumStats, setForumStats] = useState<DashboardStats | null>(null);
  const [userStats, setUserStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Fetch user dashboard data
      const userDashboard = await apiService.getDashboardData();
      setDashboardData(userDashboard);
      
      // Fetch forum stats
      const forumData = await apiService.getForumStats();
      setForumStats(forumData);
      
      // Fetch admin stats if user is admin
      if (user.role === 'admin') {
        const adminStats = await apiService.getUserStats();
        setUserStats(adminStats);
      }
      
    } catch (err: any) {
      console.error('Dashboard data fetch failed:', err);
      setError(err.message || 'Failed to load dashboard data');
      
      // Fallback to mock data for development
      if (import.meta.env.VITE_DEBUG === 'true') {
        setDashboardData({
          user: user,
          stats: {
            total_points: 1250,
            study_streak: 7,
            questions_asked: 12,
            questions_answered: 23,
            best_answer_count: 8,
          },
          recent_activity: [],
          notifications_count: 3,
        });
        
        setForumStats({
          total_questions: 156,
          total_answers: 342,
          solved_questions: 89,
          active_users: 45,
          popular_tags: [
            { name: 'programming', question_count: 23 },
            { name: 'mathematics', question_count: 18 },
            { name: 'physics', question_count: 15 },
          ],
          recent_activity: [],
        });
        
        if (user.role === 'admin') {
          setUserStats({
            total_users: 1247,
            active_users: 234,
            new_users_today: 12,
            students_count: 1089,
            teachers_count: 145,
            admins_count: 13,
          });
        }
        
        toast.info('Using mock data (development mode)');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, [user]);

  return {
    dashboardData,
    forumStats,
    userStats,
    loading,
    error,
    refetch: fetchDashboardData,
  };
};

// Mock data generators for development
export const generateMockStudentData = () => ({
  currentGPA: 3.67,
  completedCourses: 12,
  totalCredits: 45,
  studyStreak: 7,
  totalStudyHours: 127,
  badgesEarned: 15,
  forumPosts: 23,
  rank: 15,
  totalStudents: 1247,
});

export const generateMockAssignments = () => [
  {
    id: "1",
    title: "Projet React Final",
    course: "Développement Web",
    dueDate: "2024-01-20",
    priority: "high",
    progress: 65,
    timeLeft: "3 jours"
  },
  {
    id: "2",
    title: "Analyse Mathématique - Chapitre 5",
    course: "Mathématiques",
    dueDate: "2024-01-22",
    priority: "medium",
    progress: 30,
    timeLeft: "5 jours"
  },
  {
    id: "3",
    title: "Essai sur l'Intelligence Artificielle",
    course: "Informatique",
    dueDate: "2024-01-25",
    priority: "low",
    progress: 10,
    timeLeft: "8 jours"
  }
];

export const generateMockAchievements = () => [
  {
    id: "1",
    title: "Streak Master",
    description: "7 jours d'étude consécutifs",
    icon: "🔥",
    rarity: "rare",
    points: 150,
    unlockedAt: "Il y a 2 heures"
  },
  {
    id: "2",
    title: "Forum Helper",
    description: "20 réponses utiles sur le forum",
    icon: "🤝",
    rarity: "common",
    points: 100,
    unlockedAt: "Hier"
  },
  {
    id: "3",
    title: "Perfect Score",
    description: "Note parfaite en mathématiques",
    icon: "⭐",
    rarity: "epic",
    points: 300,
    unlockedAt: "Il y a 3 jours"
  }
];

export const generateMockSchedule = () => [
  {
    id: "1",
    time: "09:00",
    title: "Cours de Mathématiques",
    teacher: "Prof. Martin",
    room: "Salle 201",
    type: "course"
  },
  {
    id: "2",
    time: "11:00",
    title: "Session d'étude - React",
    description: "Travail sur le projet final",
    type: "study"
  },
  {
    id: "3",
    time: "14:00",
    title: "TP Informatique",
    teacher: "Dr. Dubois",
    room: "Lab 3",
    type: "lab"
  },
  {
    id: "4",
    time: "16:00",
    title: "Club de Programmation",
    description: "Réunion hebdomadaire",
    type: "club"
  }
];
