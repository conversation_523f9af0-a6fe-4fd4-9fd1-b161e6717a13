import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import {
  Users,
  BookOpen,
  TrendingUp,
  AlertTriangle,
  Calendar,
  MessageSquare,
  Settings,
  BarChart3,
  UserCheck,
  GraduationCap,
  Building,
  Activity,
  Clock,
  Award,
  FileText,
  Bell,
  Shield,
  Database,
  Plus,
  Loader2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useDashboard } from "@/hooks/useDashboard";
import {
  usePageEntrance,
  AnimatedBackground,
  AnimatedCard,
  AnimatedStatsCard,
  StatsSkeleton,
  CardSkeleton
} from "@/components/ui/animated-components";

const AdminDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const { dashboardData, forumStats, userStats, loading, error } = useDashboard();

  // Add page entrance animations
  usePageEntrance([]);

  // Use backend data or fallback to mock data
  const adminStats = userStats ? {
    totalStudents: userStats.students_count || 0,
    totalTeachers: userStats.teachers_count || 0,
    totalCourses: 156, // This would come from courses API
    activeUsers: userStats.active_users || 0,
    pendingApprovals: 23, // This would come from approvals API
    systemHealth: 98,
    storageUsed: 67,
    monthlyGrowth: 12.5,
    totalUsers: userStats.total_users || 0,
    newUsersToday: userStats.new_users_today || 0,
    adminsCount: userStats.admins_count || 0,
  } : {
    totalStudents: 1247,
    totalTeachers: 89,
    totalCourses: 156,
    activeUsers: 892,
    pendingApprovals: 23,
    systemHealth: 98,
    storageUsed: 67,
    monthlyGrowth: 12.5
  };

  const recentActivities = [
    {
      id: "1",
      type: "user_registration",
      message: "15 nouveaux étudiants inscrits",
      timestamp: "Il y a 2 heures",
      icon: <UserCheck className="h-4 w-4 text-green-600" />
    },
    {
      id: "2",
      type: "course_creation",
      message: "3 nouveaux cours créés",
      timestamp: "Il y a 4 heures",
      icon: <BookOpen className="h-4 w-4 text-blue-600" />
    },
    {
      id: "3",
      type: "system_alert",
      message: "Maintenance programmée ce weekend",
      timestamp: "Il y a 6 heures",
      icon: <AlertTriangle className="h-4 w-4 text-orange-600" />
    },
    {
      id: "4",
      type: "achievement",
      message: "100 badges débloqués cette semaine",
      timestamp: "Il y a 1 jour",
      icon: <Award className="h-4 w-4 text-purple-600" />
    }
  ];

  const pendingApprovals = [
    {
      id: "1",
      type: "teacher_application",
      name: "Dr. Marie Dubois",
      department: "Mathématiques",
      submittedAt: "2024-01-15",
      priority: "high"
    },
    {
      id: "2",
      type: "course_proposal",
      name: "Intelligence Artificielle Avancée",
      teacher: "Prof. Jean Martin",
      submittedAt: "2024-01-14",
      priority: "medium"
    },
    {
      id: "3",
      type: "club_creation",
      name: "Club de Robotique",
      president: "Alex Johnson",
      submittedAt: "2024-01-13",
      priority: "low"
    }
  ];

  const systemMetrics = [
    { name: "CPU Usage", value: 45, color: "bg-green-500" },
    { name: "Memory", value: 67, color: "bg-yellow-500" },
    { name: "Storage", value: 78, color: "bg-orange-500" },
    { name: "Network", value: 23, color: "bg-blue-500" }
  ];

  const topPerformingStudents = [
    { name: "Emma Wilson", gpa: 3.95, courses: 6, avatar: "/placeholder.svg" },
    { name: "Lucas Martin", gpa: 3.89, courses: 5, avatar: "/placeholder.svg" },
    { name: "Sophie Chen", gpa: 3.87, courses: 7, avatar: "/placeholder.svg" },
    { name: "Thomas Brown", gpa: 3.84, courses: 6, avatar: "/placeholder.svg" },
    { name: "Olivia Davis", gpa: 3.82, courses: 5, avatar: "/placeholder.svg" }
  ];

  // Show loading state
  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto max-w-7xl">
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold flex items-center gap-2">
                  <Shield className="h-8 w-8 text-blue-600" />
                  Chargement du Dashboard...
                </h1>
                <p className="text-muted-foreground mt-1">
                  Récupération des données administratives.
                </p>
              </div>
            </div>
          </div>

          <StatsSkeleton />

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <CardSkeleton />
              <CardSkeleton />
            </div>
            <div className="space-y-6">
              <CardSkeleton />
              <CardSkeleton />
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state for non-admin users
  if (user?.role !== 'admin') {
    return (
      <DashboardLayout>
        <div className="container mx-auto max-w-7xl">
          <div className="flex flex-col items-center justify-center min-h-[400px]">
            <Shield className="h-12 w-12 text-red-500 mb-4" />
            <h2 className="text-xl font-semibold mb-2">Accès Refusé</h2>
            <p className="text-muted-foreground mb-4">
              Vous devez être administrateur pour accéder à cette page.
            </p>
            <Button onClick={() => window.history.back()}>
              Retour
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <AnimatedBackground variant="waves" className="opacity-10" />
      <div className="container mx-auto max-w-7xl relative z-10">
        {/* Header */}
        <div className="mb-8 animate-entrance">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Shield className="h-8 w-8 text-blue-600" />
                Administration Dashboard 🛡️
              </h1>
              <p className="text-muted-foreground mt-1">
                Bienvenue, {user?.first_name || user?.username}. Gérez votre établissement en temps réel.
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Paramètres
              </Button>
              <Button>
                <Bell className="mr-2 h-4 w-4" />
                Notifications ({adminStats.pendingApprovals})
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <AnimatedStatsCard
            icon={<Users className="h-6 w-6" />}
            title="Total Étudiants"
            value={adminStats.totalStudents}
            change={adminStats.monthlyGrowth}
            delay={0.1}
          />
          <AnimatedStatsCard
            icon={<GraduationCap className="h-6 w-6" />}
            title="Enseignants"
            value={adminStats.totalTeachers}
            delay={0.2}
          />
          <AnimatedStatsCard
            icon={<BookOpen className="h-6 w-6" />}
            title="Cours Actifs"
            value={adminStats.totalCourses}
            delay={0.3}
          />
          <AnimatedStatsCard
            icon={<Activity className="h-6 w-6" />}
            title="Utilisateurs Actifs"
            value={adminStats.activeUsers}
            delay={0.4}
          />
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="users">Utilisateurs</TabsTrigger>
            <TabsTrigger value="courses">Cours</TabsTrigger>
            <TabsTrigger value="system">Système</TabsTrigger>
            <TabsTrigger value="reports">Rapports</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Recent Activities */}
              <div className="lg:col-span-2">
                <AnimatedCard
                  title="Activités Récentes"
                  description="Dernières actions sur la plateforme"
                  hoverEffect="lift"
                  entranceAnimation="slideUp"
                  delay={0.1}
                >
                  <div className="space-y-4">
                    {recentActivities.map((activity) => (
                      <div key={activity.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        {activity.icon}
                        <div className="flex-1">
                          <p className="font-medium text-sm">{activity.message}</p>
                          <p className="text-xs text-muted-foreground">{activity.timestamp}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </AnimatedCard>

                {/* System Health */}
                <AnimatedCard
                  title="État du Système"
                  description="Métriques de performance en temps réel"
                  hoverEffect="glow"
                  entranceAnimation="slideUp"
                  delay={0.3}
                  className="mt-6"
                >
                  <div className="space-y-4">
                    {systemMetrics.map((metric) => (
                      <div key={metric.name} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>{metric.name}</span>
                          <span>{metric.value}%</span>
                        </div>
                        <Progress value={metric.value} className="h-2" />
                      </div>
                    ))}
                  </div>
                </AnimatedCard>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Pending Approvals */}
                <AnimatedCard
                  title="Approbations en Attente"
                  description={`${adminStats.pendingApprovals} éléments nécessitent votre attention`}
                  hoverEffect="tilt"
                  entranceAnimation="slideLeft"
                  delay={0.2}
                >
                  <div className="space-y-3">
                    {pendingApprovals.map((approval) => (
                      <div key={approval.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm">{approval.name}</h4>
                          <Badge 
                            variant={approval.priority === 'high' ? 'destructive' : 
                                   approval.priority === 'medium' ? 'default' : 'secondary'}
                          >
                            {approval.priority}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {approval.type.replace('_', ' ')} • {approval.submittedAt}
                        </p>
                        <div className="flex gap-2 mt-2">
                          <Button size="sm" variant="outline" className="text-xs">
                            Approuver
                          </Button>
                          <Button size="sm" variant="ghost" className="text-xs">
                            Rejeter
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </AnimatedCard>

                {/* Forum Statistics (if available) */}
                {forumStats && (
                  <AnimatedCard
                    title="Statistiques du Forum"
                    description="Activité de la communauté Q&A"
                    hoverEffect="glow"
                    entranceAnimation="slideLeft"
                    delay={0.3}
                  >
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-3">
                        <div className="text-center p-2 bg-blue-50 rounded-lg">
                          <p className="text-lg font-bold text-blue-600">{forumStats.total_questions}</p>
                          <p className="text-xs text-blue-600">Questions</p>
                        </div>
                        <div className="text-center p-2 bg-green-50 rounded-lg">
                          <p className="text-lg font-bold text-green-600">{forumStats.total_answers}</p>
                          <p className="text-xs text-green-600">Réponses</p>
                        </div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Questions résolues</span>
                        <span className="font-medium">{forumStats.solved_questions}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Utilisateurs actifs</span>
                        <span className="font-medium">{forumStats.active_users}</span>
                      </div>

                      {forumStats.popular_tags && forumStats.popular_tags.length > 0 && (
                        <div className="mt-3">
                          <p className="text-sm font-medium mb-2">Tags populaires</p>
                          <div className="flex flex-wrap gap-1">
                            {forumStats.popular_tags.slice(0, 3).map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {tag.name}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </AnimatedCard>
                )}

                {/* Quick Actions */}
                <AnimatedCard
                  title="Actions Rapides"
                  hoverEffect="scale"
                  entranceAnimation="slideLeft"
                  delay={0.4}
                >
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" size="sm" className="h-auto p-3 flex flex-col gap-1">
                      <Users className="h-4 w-4" />
                      <span className="text-xs">Ajouter Utilisateur</span>
                    </Button>
                    <Button variant="outline" size="sm" className="h-auto p-3 flex flex-col gap-1">
                      <BookOpen className="h-4 w-4" />
                      <span className="text-xs">Nouveau Cours</span>
                    </Button>
                    <Button variant="outline" size="sm" className="h-auto p-3 flex flex-col gap-1">
                      <MessageSquare className="h-4 w-4" />
                      <span className="text-xs">Annonce</span>
                    </Button>
                    <Button variant="outline" size="sm" className="h-auto p-3 flex flex-col gap-1">
                      <BarChart3 className="h-4 w-4" />
                      <span className="text-xs">Rapport</span>
                    </Button>
                  </div>
                </AnimatedCard>
              </div>
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AnimatedCard
                title="Meilleurs Étudiants"
                description="Top 5 des étudiants par GPA"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-3">
                  {topPerformingStudents.map((student, index) => (
                    <div key={student.name} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50">
                      <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs font-bold">
                        {index + 1}
                      </div>
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={student.avatar} />
                        <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <p className="font-medium text-sm">{student.name}</p>
                        <p className="text-xs text-muted-foreground">{student.courses} cours</p>
                      </div>
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        {student.gpa}
                      </Badge>
                    </div>
                  ))}
                </div>
              </AnimatedCard>

              <AnimatedCard
                title="Statistiques Utilisateurs"
                description="Répartition et activité des utilisateurs"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <p className="text-2xl font-bold text-blue-600">{adminStats.totalStudents}</p>
                      <p className="text-sm text-blue-600">Étudiants</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <p className="text-2xl font-bold text-green-600">{adminStats.totalTeachers}</p>
                      <p className="text-sm text-green-600">Enseignants</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Utilisateurs actifs aujourd'hui</span>
                      <span className="font-medium">{adminStats.activeUsers}</span>
                    </div>
                    <Progress value={(adminStats.activeUsers / (adminStats.totalStudents + adminStats.totalTeachers)) * 100} />
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Courses Tab */}
          <TabsContent value="courses">
            <AnimatedCard
              title="Gestion des Cours"
              description="Vue d'ensemble des cours et programmes"
              hoverEffect="lift"
              entranceAnimation="fadeIn"
              delay={0.1}
            >
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Gestion des Cours</h3>
                <p className="text-muted-foreground mb-4">
                  Interface de gestion des cours en développement
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Ajouter un Cours
                </Button>
              </div>
            </AnimatedCard>
          </TabsContent>

          {/* System Tab */}
          <TabsContent value="system">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AnimatedCard
                title="Santé du Système"
                description="Monitoring en temps réel"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Santé Globale</span>
                    <Badge className="bg-green-100 text-green-800">
                      {adminStats.systemHealth}% Excellent
                    </Badge>
                  </div>
                  <Progress value={adminStats.systemHealth} className="h-3" />
                  
                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <Database className="h-6 w-6 text-green-600 mx-auto mb-1" />
                      <p className="text-sm font-medium">Base de Données</p>
                      <p className="text-xs text-green-600">Opérationnelle</p>
                    </div>
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <Activity className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                      <p className="text-sm font-medium">API</p>
                      <p className="text-xs text-blue-600">Réactive</p>
                    </div>
                  </div>
                </div>
              </AnimatedCard>

              <AnimatedCard
                title="Stockage et Ressources"
                description="Utilisation des ressources système"
                hoverEffect="tilt"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Stockage utilisé</span>
                      <span>{adminStats.storageUsed}% de 1TB</span>
                    </div>
                    <Progress value={adminStats.storageUsed} />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div className="p-2 bg-muted/50 rounded">
                      <p className="text-xs text-muted-foreground">Documents</p>
                      <p className="font-medium">245GB</p>
                    </div>
                    <div className="p-2 bg-muted/50 rounded">
                      <p className="text-xs text-muted-foreground">Médias</p>
                      <p className="font-medium">387GB</p>
                    </div>
                    <div className="p-2 bg-muted/50 rounded">
                      <p className="text-xs text-muted-foreground">Backups</p>
                      <p className="font-medium">38GB</p>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports">
            <AnimatedCard
              title="Rapports et Analytics"
              description="Génération de rapports détaillés"
              hoverEffect="lift"
              entranceAnimation="fadeIn"
              delay={0.1}
            >
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Centre de Rapports</h3>
                <p className="text-muted-foreground mb-4">
                  Générez des rapports détaillés sur l'activité de votre établissement
                </p>
                <div className="flex gap-2 justify-center">
                  <Button variant="outline">
                    <FileText className="mr-2 h-4 w-4" />
                    Rapport Mensuel
                  </Button>
                  <Button>
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Analytics
                  </Button>
                </div>
              </div>
            </AnimatedCard>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default AdminDashboard;
