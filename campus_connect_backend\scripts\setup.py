#!/usr/bin/env python
"""
Setup script for Campus Connect Backend
Creates initial data and superuser
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'campus_connect.settings')
django.setup()

from django.contrib.auth import get_user_model
from accounts.models import UserProfile
from courses.models import Department
from forum.models import Tag

User = get_user_model()


def create_superuser():
    """Create superuser if it doesn't exist"""
    if not User.objects.filter(is_superuser=True).exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='Campus',
            last_name='Admin',
            role='admin'
        )
        print("✅ Superuser created: <EMAIL> / admin123")
    else:
        print("ℹ️  Superuser already exists")


def create_sample_users():
    """Create sample users for testing"""
    sample_users = [
        {
            'username': 'marie.dupont',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': '<PERSON><PERSON>',
            'role': 'student',
            'password': 'student123'
        },
        {
            'username': 'jean.martin',
            'email': '<EMAIL>',
            'first_name': 'Jean',
            'last_name': 'Martin',
            'role': 'teacher',
            'password': 'teacher123'
        },
        {
            'username': 'sophie.admin',
            'email': '<EMAIL>',
            'first_name': 'Sophie',
            'last_name': 'Admin',
            'role': 'admin',
            'password': 'admin123'
        }
    ]
    
    for user_data in sample_users:
        if not User.objects.filter(email=user_data['email']).exists():
            password = user_data.pop('password')
            user = User.objects.create_user(**user_data)
            user.set_password(password)
            user.save()
            
            # Create profile
            UserProfile.objects.get_or_create(user=user)
            
            print(f"✅ Created user: {user.email}")
        else:
            print(f"ℹ️  User already exists: {user_data['email']}")


def create_departments():
    """Create sample departments"""
    departments = [
        {'name': 'Computer Science', 'code': 'CS'},
        {'name': 'Mathematics', 'code': 'MATH'},
        {'name': 'Physics', 'code': 'PHYS'},
        {'name': 'Engineering', 'code': 'ENG'},
        {'name': 'Business Administration', 'code': 'BUS'},
    ]
    
    for dept_data in departments:
        dept, created = Department.objects.get_or_create(**dept_data)
        if created:
            print(f"✅ Created department: {dept.name}")
        else:
            print(f"ℹ️  Department already exists: {dept.name}")


def create_forum_tags():
    """Create initial forum tags"""
    tags = [
        'programming', 'python', 'javascript', 'react', 'django',
        'mathematics', 'calculus', 'algebra', 'statistics',
        'physics', 'chemistry', 'biology',
        'study-tips', 'homework-help', 'exam-prep',
        'career-advice', 'internships', 'projects'
    ]
    
    for tag_name in tags:
        tag, created = Tag.objects.get_or_create(name=tag_name)
        if created:
            print(f"✅ Created tag: {tag.name}")


def main():
    """Main setup function"""
    print("🚀 Setting up Campus Connect Backend...")
    
    # Run migrations first
    print("\n📦 Running migrations...")
    execute_from_command_line(['manage.py', 'migrate'])
    
    # Create initial data
    print("\n👤 Creating users...")
    create_superuser()
    create_sample_users()
    
    print("\n🏫 Creating departments...")
    create_departments()
    
    print("\n🏷️  Creating forum tags...")
    create_forum_tags()
    
    print("\n✅ Setup completed successfully!")
    print("\n📋 Login credentials:")
    print("   Admin: <EMAIL> / admin123")
    print("   Student: <EMAIL> / student123")
    print("   Teacher: <EMAIL> / teacher123")


if __name__ == '__main__':
    main()
