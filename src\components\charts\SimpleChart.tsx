import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface ChartData {
  label: string;
  value: number;
  color?: string;
}

interface SimpleChartProps {
  data: ChartData[];
  type: 'bar' | 'line' | 'pie' | 'area';
  width?: number;
  height?: number;
  className?: string;
  animated?: boolean;
}

const SimpleChart = ({ 
  data, 
  type, 
  width = 400, 
  height = 200, 
  className = '',
  animated = true 
}: SimpleChartProps) => {
  const chartRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!chartRef.current || !data.length) return;

    const container = chartRef.current;
    container.innerHTML = ''; // Clear previous chart

    switch (type) {
      case 'bar':
        renderBarChart(container, data, width, height, animated);
        break;
      case 'line':
        renderLineChart(container, data, width, height, animated);
        break;
      case 'pie':
        renderPieChart(container, data, width, height, animated);
        break;
      case 'area':
        renderAreaChart(container, data, width, height, animated);
        break;
    }
  }, [data, type, width, height, animated]);

  return (
    <div 
      ref={chartRef}
      className={`chart-container ${className}`}
      style={{ width, height }}
    />
  );
};

const renderBarChart = (container: HTMLElement, data: ChartData[], width: number, height: number, animated: boolean) => {
  const maxValue = Math.max(...data.map(d => d.value));
  const barWidth = (width - 40) / data.length - 10;
  const chartHeight = height - 40;

  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('width', width.toString());
  svg.setAttribute('height', height.toString());
  svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

  data.forEach((item, index) => {
    const barHeight = (item.value / maxValue) * chartHeight;
    const x = 20 + index * (barWidth + 10);
    const y = height - 20 - barHeight;

    // Bar
    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    rect.setAttribute('x', x.toString());
    rect.setAttribute('y', (height - 20).toString());
    rect.setAttribute('width', barWidth.toString());
    rect.setAttribute('height', '0');
    rect.setAttribute('fill', item.color || '#3B82F6');
    rect.setAttribute('rx', '4');
    svg.appendChild(rect);

    // Label
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', (x + barWidth / 2).toString());
    text.setAttribute('y', (height - 5).toString());
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('font-size', '12');
    text.setAttribute('fill', '#6B7280');
    text.textContent = item.label;
    svg.appendChild(text);

    // Value label
    const valueText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    valueText.setAttribute('x', (x + barWidth / 2).toString());
    valueText.setAttribute('y', (y - 5).toString());
    valueText.setAttribute('text-anchor', 'middle');
    valueText.setAttribute('font-size', '10');
    valueText.setAttribute('fill', '#374151');
    valueText.setAttribute('opacity', '0');
    valueText.textContent = item.value.toString();
    svg.appendChild(valueText);

    if (animated) {
      gsap.to(rect, {
        height: barHeight,
        y: y,
        duration: 0.8,
        delay: index * 0.1,
        ease: "power2.out"
      });

      gsap.to(valueText, {
        opacity: 1,
        duration: 0.3,
        delay: index * 0.1 + 0.5
      });
    } else {
      rect.setAttribute('height', barHeight.toString());
      rect.setAttribute('y', y.toString());
      valueText.setAttribute('opacity', '1');
    }
  });

  container.appendChild(svg);
};

const renderLineChart = (container: HTMLElement, data: ChartData[], width: number, height: number, animated: boolean) => {
  const maxValue = Math.max(...data.map(d => d.value));
  const chartHeight = height - 40;
  const chartWidth = width - 40;

  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('width', width.toString());
  svg.setAttribute('height', height.toString());
  svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

  // Create path
  let pathData = '';
  const points: { x: number; y: number }[] = [];

  data.forEach((item, index) => {
    const x = 20 + (index / (data.length - 1)) * chartWidth;
    const y = 20 + (1 - item.value / maxValue) * chartHeight;
    points.push({ x, y });

    if (index === 0) {
      pathData += `M ${x} ${y}`;
    } else {
      pathData += ` L ${x} ${y}`;
    }

    // Point circle
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    circle.setAttribute('cx', x.toString());
    circle.setAttribute('cy', y.toString());
    circle.setAttribute('r', '4');
    circle.setAttribute('fill', item.color || '#3B82F6');
    circle.setAttribute('stroke', '#ffffff');
    circle.setAttribute('stroke-width', '2');
    svg.appendChild(circle);

    // Label
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', x.toString());
    text.setAttribute('y', (height - 5).toString());
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('font-size', '12');
    text.setAttribute('fill', '#6B7280');
    text.textContent = item.label;
    svg.appendChild(text);

    if (animated) {
      gsap.from(circle, {
        scale: 0,
        duration: 0.3,
        delay: index * 0.1 + 0.5,
        ease: "back.out(1.7)"
      });
    }
  });

  // Line path
  const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  path.setAttribute('d', pathData);
  path.setAttribute('stroke', data[0]?.color || '#3B82F6');
  path.setAttribute('stroke-width', '2');
  path.setAttribute('fill', 'none');
  path.setAttribute('stroke-linecap', 'round');
  path.setAttribute('stroke-linejoin', 'round');
  svg.insertBefore(path, svg.firstChild);

  if (animated) {
    const pathLength = path.getTotalLength();
    path.setAttribute('stroke-dasharray', pathLength.toString());
    path.setAttribute('stroke-dashoffset', pathLength.toString());

    gsap.to(path, {
      strokeDashoffset: 0,
      duration: 1.5,
      ease: "power2.out"
    });
  }

  container.appendChild(svg);
};

const renderPieChart = (container: HTMLElement, data: ChartData[], width: number, height: number, animated: boolean) => {
  const radius = Math.min(width, height) / 2 - 20;
  const centerX = width / 2;
  const centerY = height / 2;
  const total = data.reduce((sum, item) => sum + item.value, 0);

  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('width', width.toString());
  svg.setAttribute('height', height.toString());
  svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

  let currentAngle = -90; // Start from top

  data.forEach((item, index) => {
    const angle = (item.value / total) * 360;
    const startAngle = currentAngle;
    const endAngle = currentAngle + angle;

    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;

    const x1 = centerX + radius * Math.cos(startAngleRad);
    const y1 = centerY + radius * Math.sin(startAngleRad);
    const x2 = centerX + radius * Math.cos(endAngleRad);
    const y2 = centerY + radius * Math.sin(endAngleRad);

    const largeArcFlag = angle > 180 ? 1 : 0;

    const pathData = [
      `M ${centerX} ${centerY}`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ');

    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('d', pathData);
    path.setAttribute('fill', item.color || `hsl(${index * 60}, 70%, 60%)`);
    path.setAttribute('stroke', '#ffffff');
    path.setAttribute('stroke-width', '2');
    svg.appendChild(path);

    // Label
    const labelAngle = (startAngle + endAngle) / 2;
    const labelAngleRad = (labelAngle * Math.PI) / 180;
    const labelRadius = radius * 0.7;
    const labelX = centerX + labelRadius * Math.cos(labelAngleRad);
    const labelY = centerY + labelRadius * Math.sin(labelAngleRad);

    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    text.setAttribute('x', labelX.toString());
    text.setAttribute('y', labelY.toString());
    text.setAttribute('text-anchor', 'middle');
    text.setAttribute('dominant-baseline', 'middle');
    text.setAttribute('font-size', '12');
    text.setAttribute('fill', '#ffffff');
    text.setAttribute('font-weight', 'bold');
    text.textContent = `${Math.round((item.value / total) * 100)}%`;
    svg.appendChild(text);

    if (animated) {
      gsap.from(path, {
        scale: 0,
        transformOrigin: `${centerX}px ${centerY}px`,
        duration: 0.6,
        delay: index * 0.1,
        ease: "back.out(1.7)"
      });

      gsap.from(text, {
        opacity: 0,
        scale: 0,
        duration: 0.3,
        delay: index * 0.1 + 0.3
      });
    }

    currentAngle += angle;
  });

  container.appendChild(svg);
};

const renderAreaChart = (container: HTMLElement, data: ChartData[], width: number, height: number, animated: boolean) => {
  const maxValue = Math.max(...data.map(d => d.value));
  const chartHeight = height - 40;
  const chartWidth = width - 40;

  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('width', width.toString());
  svg.setAttribute('height', height.toString());
  svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

  // Create gradient
  const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
  const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
  gradient.setAttribute('id', 'areaGradient');
  gradient.setAttribute('x1', '0%');
  gradient.setAttribute('y1', '0%');
  gradient.setAttribute('x2', '0%');
  gradient.setAttribute('y2', '100%');

  const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
  stop1.setAttribute('offset', '0%');
  stop1.setAttribute('stop-color', data[0]?.color || '#3B82F6');
  stop1.setAttribute('stop-opacity', '0.8');

  const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
  stop2.setAttribute('offset', '100%');
  stop2.setAttribute('stop-color', data[0]?.color || '#3B82F6');
  stop2.setAttribute('stop-opacity', '0.1');

  gradient.appendChild(stop1);
  gradient.appendChild(stop2);
  defs.appendChild(gradient);
  svg.appendChild(defs);

  // Create area path
  let pathData = '';
  data.forEach((item, index) => {
    const x = 20 + (index / (data.length - 1)) * chartWidth;
    const y = 20 + (1 - item.value / maxValue) * chartHeight;

    if (index === 0) {
      pathData += `M ${x} ${height - 20}`;
      pathData += ` L ${x} ${y}`;
    } else {
      pathData += ` L ${x} ${y}`;
    }
  });

  // Close the area
  pathData += ` L ${20 + chartWidth} ${height - 20}`;
  pathData += ` Z`;

  const areaPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  areaPath.setAttribute('d', pathData);
  areaPath.setAttribute('fill', 'url(#areaGradient)');
  svg.appendChild(areaPath);

  // Add line on top
  let lineData = '';
  data.forEach((item, index) => {
    const x = 20 + (index / (data.length - 1)) * chartWidth;
    const y = 20 + (1 - item.value / maxValue) * chartHeight;

    if (index === 0) {
      lineData += `M ${x} ${y}`;
    } else {
      lineData += ` L ${x} ${y}`;
    }
  });

  const linePath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  linePath.setAttribute('d', lineData);
  linePath.setAttribute('stroke', data[0]?.color || '#3B82F6');
  linePath.setAttribute('stroke-width', '2');
  linePath.setAttribute('fill', 'none');
  svg.appendChild(linePath);

  if (animated) {
    gsap.from(areaPath, {
      scaleY: 0,
      transformOrigin: 'bottom',
      duration: 1,
      ease: "power2.out"
    });

    const lineLength = linePath.getTotalLength();
    linePath.setAttribute('stroke-dasharray', lineLength.toString());
    linePath.setAttribute('stroke-dashoffset', lineLength.toString());

    gsap.to(linePath, {
      strokeDashoffset: 0,
      duration: 1.5,
      delay: 0.3,
      ease: "power2.out"
    });
  }

  container.appendChild(svg);
};

export default SimpleChart;
