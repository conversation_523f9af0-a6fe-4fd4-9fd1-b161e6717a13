# 🧪 Guide de Test - Campus Connect

Ce guide vous aide à tester l'intégration complète entre le frontend React et le backend Django.

## 🚀 **Démarrage Rapide**

### **1. Démarrer le Backend Django**
```bash
cd campus_connect_backend
source venv/bin/activate  # Windows: venv\Scripts\activate
python manage.py runserver
```
✅ **Backend disponible** : http://localhost:8000

### **2. Démarrer le Frontend React**
```bash
# Dans le dossier racine du projet
npm run dev
```
✅ **Frontend disponible** : http://localhost:8081

## 🔍 **Tests à Effectuer**

### **1. Test d'Authentification**

#### **Mode Production (avec Backend)**
1. **Aller sur** http://localhost:8081
2. **Cliquer** sur "Se connecter"
3. **Utiliser les comptes de test** :
   ```
   Admin: <EMAIL> / admin123
   Étudiant: <EMAIL> / student123
   Enseignant: <EMAIL> / teacher123
   ```
4. **Vérifier** que la connexion fonctionne et redirige vers le dashboard

#### **Mode Développement (Mock Data)**
Si le backend n'est pas disponible, l'app utilise automatiquement les données mock :
1. **Email** : n'importe quel email des comptes mock
2. **Mot de passe** : `password`

### **2. Test du Forum Q&A**

#### **Navigation**
1. **Aller** dans "Forum" depuis le menu
2. **Vérifier** que les questions se chargent
3. **Tester** les onglets : "Toutes", "Mes questions", "Non résolues", "Résolues"

#### **Recherche et Filtres**
1. **Utiliser** la barre de recherche
2. **Tester** les filtres par tags
3. **Vérifier** que les résultats se mettent à jour

#### **Créer une Question**
1. **Cliquer** sur "Poser une Question"
2. **Remplir** le formulaire :
   - Titre (minimum 10 caractères)
   - Description (minimum 20 caractères)
   - Tags (au moins 1 requis)
3. **Publier** et vérifier la redirection

#### **Répondre à une Question**
1. **Cliquer** sur une question
2. **Utiliser** les boutons de vote (upvote/downvote)
3. **Écrire** une réponse (minimum 10 caractères)
4. **Publier** la réponse

#### **Accepter une Réponse** (si vous êtes l'auteur)
1. **Cliquer** sur "Accepter cette réponse"
2. **Vérifier** que la question devient "Résolue"

### **3. Test des Dashboards**

#### **Dashboard Étudiant**
1. **Vérifier** les statistiques personnelles
2. **Contrôler** les données du forum
3. **Tester** les animations et le mode sombre

#### **Dashboard Admin** (compte admin requis)
1. **Voir** les statistiques globales
2. **Vérifier** les métriques du forum
3. **Contrôler** les données utilisateurs

### **4. Test de l'Interface**

#### **Mode Sombre**
1. **Cliquer** sur l'icône de thème dans la navigation
2. **Vérifier** que tous les composants s'adaptent

#### **Responsive Design**
1. **Redimensionner** la fenêtre
2. **Tester** sur mobile (F12 > mode mobile)
3. **Vérifier** que la navigation mobile fonctionne

## 🐛 **Dépannage**

### **Erreurs Communes**

#### **1. "Network Error" ou "Failed to fetch"**
- ✅ **Vérifier** que le backend Django tourne sur le port 8000
- ✅ **Contrôler** les variables d'environnement dans `.env.local`
- ✅ **L'app basculera automatiquement** en mode mock si le backend n'est pas disponible

#### **2. "CORS Error"**
- ✅ **Le backend** est configuré pour accepter les requêtes du frontend
- ✅ **Redémarrer** le backend Django si nécessaire

#### **3. "Token expired" ou erreurs d'authentification**
- ✅ **Se déconnecter** et se reconnecter
- ✅ **Vider** le localStorage du navigateur (F12 > Application > Local Storage)

#### **4. Données ne se chargent pas**
- ✅ **Ouvrir** les DevTools (F12) et vérifier la console
- ✅ **Vérifier** l'onglet Network pour voir les requêtes API
- ✅ **En mode développement**, les données mock s'affichent automatiquement

### **Logs Utiles**

#### **Frontend (Console du navigateur)**
```javascript
// Vérifier l'état de l'authentification
localStorage.getItem('access_token')

// Vérifier les erreurs API
// Ouvrir F12 > Console pour voir les logs
```

#### **Backend (Terminal Django)**
```bash
# Voir les requêtes API en temps réel
python manage.py runserver --verbosity=2
```

## ✅ **Checklist de Validation**

### **Fonctionnalités de Base**
- [ ] Connexion/Déconnexion fonctionne
- [ ] Navigation entre les pages
- [ ] Mode sombre adaptatif
- [ ] Interface responsive

### **Forum Q&A**
- [ ] Affichage des questions
- [ ] Recherche et filtres
- [ ] Création de questions
- [ ] Système de votes
- [ ] Réponses et acceptation
- [ ] Tags dynamiques

### **Dashboards**
- [ ] Statistiques personnelles (étudiant)
- [ ] Métriques globales (admin)
- [ ] Données en temps réel
- [ ] États de chargement

### **Intégration Backend**
- [ ] API Django accessible
- [ ] Authentification JWT
- [ ] Données persistantes
- [ ] Gestion d'erreurs

## 🎯 **Scénarios de Test Complets**

### **Scénario 1 : Nouvel Utilisateur**
1. **S'inscrire** avec un nouveau compte
2. **Explorer** le forum
3. **Poser** une première question
4. **Recevoir** et accepter une réponse

### **Scénario 2 : Utilisateur Actif**
1. **Se connecter** avec un compte existant
2. **Répondre** à plusieurs questions
3. **Voter** sur du contenu
4. **Vérifier** les statistiques mises à jour

### **Scénario 3 : Administrateur**
1. **Se connecter** en tant qu'admin
2. **Consulter** les statistiques globales
3. **Modérer** le contenu si nécessaire
4. **Analyser** l'activité de la communauté

## 🚀 **Prêt pour la Production**

Si tous les tests passent, votre application Campus Connect est prête pour :
- ✅ **Déploiement** sur Railway (backend)
- ✅ **Déploiement** sur Vercel/Netlify (frontend)
- ✅ **Utilisation** par une vraie communauté étudiante

**Félicitations ! Votre portail étudiant avec forum Q&A est opérationnel ! 🎉**
