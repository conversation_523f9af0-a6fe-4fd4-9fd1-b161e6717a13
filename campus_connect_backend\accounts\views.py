from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login, logout
from django.utils import timezone
from django.db.models import Count, Q
from datetime import timedelta

from .models import User, UserProfile, UserSession
from .serializers import (
    UserSerializer, UserProfileSerializer, RegisterSerializer,
    LoginSerializer, ChangePasswordSerializer, PasswordResetSerializer,
    PasswordResetConfirmSerializer, UserSessionSerializer,
    UserStatsSerializer, ProfileUpdateSerializer
)


class RegisterView(generics.CreateAPIView):
    """
    User registration endpoint
    POST /api/auth/register/
    """
    queryset = User.objects.all()
    serializer_class = RegisterSerializer
    permission_classes = [permissions.AllowAny]
    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        
        return Response({
            'user': UserSerializer(user).data,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            },
            'message': 'Registration successful'
        }, status=status.HTTP_201_CREATED)


class LoginView(APIView):
    """
    User login endpoint
    POST /api/auth/login/
    """
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user = serializer.validated_data['user']
        remember_me = serializer.validated_data.get('remember_me', False)
        
        # Create user session
        self.create_user_session(request, user)
        
        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        
        # Set token expiry based on remember_me
        if remember_me:
            refresh.set_exp(lifetime=timedelta(days=30))
        
        return Response({
            'user': UserSerializer(user).data,
            'tokens': {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            },
            'message': f'Welcome back, {user.first_name}!'
        })
    
    def create_user_session(self, request, user):
        """Create or update user session"""
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        UserSession.objects.create(
            user=user,
            session_key=request.session.session_key or 'api_session',
            ip_address=ip_address,
            user_agent=user_agent,
            device_type=self.get_device_type(user_agent)
        )
        
        # Update user's last login IP
        user.last_login_ip = ip_address
        user.save(update_fields=['last_login_ip'])
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def get_device_type(self, user_agent):
        """Determine device type from user agent"""
        user_agent = user_agent.lower()
        if 'mobile' in user_agent or 'android' in user_agent or 'iphone' in user_agent:
            return 'mobile'
        elif 'tablet' in user_agent or 'ipad' in user_agent:
            return 'tablet'
        else:
            return 'desktop'


class LogoutView(APIView):
    """
    User logout endpoint
    POST /api/auth/logout/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        try:
            refresh_token = request.data.get('refresh_token')
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            
            # Deactivate user sessions
            UserSession.objects.filter(
                user=request.user,
                is_active=True
            ).update(is_active=False)
            
            return Response({
                'message': 'Successfully logged out'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'error': 'Invalid token'
            }, status=status.HTTP_400_BAD_REQUEST)


class ProfileView(generics.RetrieveUpdateAPIView):
    """
    User profile endpoint
    GET/PUT /api/auth/profile/
    """
    serializer_class = ProfileUpdateSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        return self.request.user
    
    def get(self, request, *args, **kwargs):
        user = self.get_object()
        user_data = UserSerializer(user).data
        
        # Include profile data if exists
        try:
            profile_data = UserProfileSerializer(user.profile).data
            user_data['profile'] = profile_data
        except UserProfile.DoesNotExist:
            UserProfile.objects.create(user=user)
            user_data['profile'] = {}
        
        return Response(user_data)


class ChangePasswordView(APIView):
    """
    Change password endpoint
    POST /api/auth/change-password/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = ChangePasswordSerializer(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        
        user = request.user
        user.set_password(serializer.validated_data['new_password'])
        user.save()
        
        return Response({
            'message': 'Password changed successfully'
        })


class UserStatsView(APIView):
    """
    User statistics for admin dashboard
    GET /api/auth/stats/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        if request.user.role != 'admin':
            return Response(
                {'error': 'Admin access required'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        today = timezone.now().date()
        
        stats = {
            'total_users': User.objects.count(),
            'active_users': User.objects.filter(
                last_login__gte=timezone.now() - timedelta(days=30)
            ).count(),
            'new_users_today': User.objects.filter(
                date_joined__date=today
            ).count(),
            'students_count': User.objects.filter(role='student').count(),
            'teachers_count': User.objects.filter(role='teacher').count(),
            'admins_count': User.objects.filter(role='admin').count(),
        }
        
        serializer = UserStatsSerializer(stats)
        return Response(serializer.data)


class UserSessionsView(generics.ListAPIView):
    """
    User sessions list
    GET /api/auth/sessions/
    """
    serializer_class = UserSessionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserSession.objects.filter(
            user=self.request.user,
            is_active=True
        ).order_by('-last_activity')


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def terminate_session(request, session_id):
    """
    Terminate a specific user session
    POST /api/auth/sessions/{session_id}/terminate/
    """
    try:
        session = UserSession.objects.get(
            id=session_id,
            user=request.user
        )
        session.is_active = False
        session.save()
        
        return Response({
            'message': 'Session terminated successfully'
        })
    except UserSession.DoesNotExist:
        return Response({
            'error': 'Session not found'
        }, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def user_dashboard_data(request):
    """
    Get user-specific dashboard data
    GET /api/auth/dashboard-data/
    """
    user = request.user
    
    # Get user profile with stats
    try:
        profile = user.profile
    except UserProfile.DoesNotExist:
        profile = UserProfile.objects.create(user=user)
    
    dashboard_data = {
        'user': UserSerializer(user).data,
        'stats': {
            'total_points': profile.total_points,
            'study_streak': profile.study_streak,
            'questions_asked': profile.questions_asked,
            'questions_answered': profile.questions_answered,
            'best_answer_count': profile.best_answer_count,
        },
        'recent_activity': [],  # Will be populated by other apps
        'notifications_count': 0,  # Will be populated by notifications app
    }
    
    return Response(dashboard_data)
