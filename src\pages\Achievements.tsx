import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { 
  Trophy, 
  Star, 
  Target, 
  Zap, 
  Crown, 
  Medal,
  Award,
  TrendingUp,
  Calendar,
  BookOpen,
  MessageSquare,
  Users,
  Clock,
  CheckCircle,
  Lock
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: "academic" | "social" | "consistency" | "special";
  points: number;
  rarity: "common" | "rare" | "epic" | "legendary";
  unlocked: boolean;
  unlockedAt?: Date;
  progress?: number;
  maxProgress?: number;
  requirements: string[];
}

interface UserStats {
  totalPoints: number;
  level: number;
  rank: number;
  totalUsers: number;
  streakDays: number;
  completedAssignments: number;
  forumPosts: number;
  studyHours: number;
}

const Achievements = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");

  // Mock user stats
  const [userStats] = useState<UserStats>({
    totalPoints: 2450,
    level: 12,
    rank: 23,
    totalUsers: 1250,
    streakDays: 15,
    completedAssignments: 28,
    forumPosts: 45,
    studyHours: 127
  });

  // Mock achievements data
  const [achievements] = useState<Achievement[]>([
    {
      id: "first-assignment",
      title: "First Steps",
      description: "Complete your first assignment",
      icon: "🎯",
      category: "academic",
      points: 50,
      rarity: "common",
      unlocked: true,
      unlockedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      requirements: ["Complete 1 assignment"]
    },
    {
      id: "assignment-master",
      title: "Assignment Master",
      description: "Complete 25 assignments",
      icon: "📚",
      category: "academic",
      points: 500,
      rarity: "rare",
      unlocked: true,
      unlockedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      progress: 28,
      maxProgress: 25,
      requirements: ["Complete 25 assignments"]
    },
    {
      id: "perfect-week",
      title: "Perfect Week",
      description: "Complete all assignments for a week",
      icon: "⭐",
      category: "consistency",
      points: 200,
      rarity: "rare",
      unlocked: true,
      unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      requirements: ["Complete all assignments in one week"]
    },
    {
      id: "social-butterfly",
      title: "Social Butterfly",
      description: "Make 50 forum posts",
      icon: "🦋",
      category: "social",
      points: 300,
      rarity: "rare",
      unlocked: false,
      progress: 45,
      maxProgress: 50,
      requirements: ["Make 50 forum posts"]
    },
    {
      id: "study-streak",
      title: "Study Streak",
      description: "Study for 30 consecutive days",
      icon: "🔥",
      category: "consistency",
      points: 750,
      rarity: "epic",
      unlocked: false,
      progress: 15,
      maxProgress: 30,
      requirements: ["Study for 30 consecutive days"]
    },
    {
      id: "night-owl",
      title: "Night Owl",
      description: "Study after 10 PM for 10 sessions",
      icon: "🦉",
      category: "special",
      points: 150,
      rarity: "common",
      unlocked: false,
      progress: 3,
      maxProgress: 10,
      requirements: ["Study after 10 PM for 10 sessions"]
    },
    {
      id: "early-bird",
      title: "Early Bird",
      description: "Study before 7 AM for 10 sessions",
      icon: "🐦",
      category: "special",
      points: 150,
      rarity: "common",
      unlocked: false,
      progress: 0,
      maxProgress: 10,
      requirements: ["Study before 7 AM for 10 sessions"]
    },
    {
      id: "scholar",
      title: "Scholar",
      description: "Reach level 20",
      icon: "🎓",
      category: "academic",
      points: 1000,
      rarity: "epic",
      unlocked: false,
      progress: 12,
      maxProgress: 20,
      requirements: ["Reach level 20"]
    },
    {
      id: "legend",
      title: "Legend",
      description: "Reach top 10 in leaderboard",
      icon: "👑",
      category: "special",
      points: 2000,
      rarity: "legendary",
      unlocked: false,
      progress: 23,
      maxProgress: 10,
      requirements: ["Reach top 10 in leaderboard"]
    }
  ]);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "common": return "text-gray-600 bg-gray-100 border-gray-300";
      case "rare": return "text-blue-600 bg-blue-100 border-blue-300";
      case "epic": return "text-purple-600 bg-purple-100 border-purple-300";
      case "legendary": return "text-yellow-600 bg-yellow-100 border-yellow-300";
      default: return "text-gray-600 bg-gray-100 border-gray-300";
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "academic": return <BookOpen className="h-4 w-4" />;
      case "social": return <Users className="h-4 w-4" />;
      case "consistency": return <Calendar className="h-4 w-4" />;
      case "special": return <Star className="h-4 w-4" />;
      default: return <Trophy className="h-4 w-4" />;
    }
  };

  const getFilteredAchievements = (filter: string) => {
    switch (filter) {
      case "unlocked":
        return achievements.filter(a => a.unlocked);
      case "locked":
        return achievements.filter(a => !a.unlocked);
      case "academic":
      case "social":
      case "consistency":
      case "special":
        return achievements.filter(a => a.category === filter);
      default:
        return achievements;
    }
  };

  const getNextLevelPoints = () => {
    return userStats.level * 250; // 250 points per level
  };

  const getCurrentLevelPoints = () => {
    return userStats.totalPoints % 250;
  };

  const getLevelProgress = () => {
    return (getCurrentLevelPoints() / 250) * 100;
  };

  // Mock leaderboard data
  const leaderboard = [
    { rank: 1, name: "Alice Johnson", points: 3250, avatar: "/placeholder.svg" },
    { rank: 2, name: "Bob Smith", points: 3100, avatar: "/placeholder.svg" },
    { rank: 3, name: "Carol Davis", points: 2980, avatar: "/placeholder.svg" },
    { rank: 4, name: "David Wilson", points: 2850, avatar: "/placeholder.svg" },
    { rank: 5, name: "Eva Brown", points: 2720, avatar: "/placeholder.svg" },
    { rank: 23, name: `${user?.firstName} ${user?.lastName}`, points: userStats.totalPoints, avatar: user?.profilePicture }
  ];

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-6xl">
        <div className="mb-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Trophy className="h-6 w-6 text-yellow-600" />
            Achievements & Leaderboard
          </h1>
          <p className="text-muted-foreground">
            Track your progress and compete with other students
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
            <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
            <TabsTrigger value="challenges">Challenges</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* User Stats */}
              <div className="lg:col-span-2 space-y-6">
                {/* Level Progress */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Crown className="h-5 w-5 text-yellow-600" />
                      Level {userStats.level}
                    </CardTitle>
                    <CardDescription>
                      {getCurrentLevelPoints()}/{getNextLevelPoints()} XP to next level
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Progress value={getLevelProgress()} className="mb-2" />
                    <p className="text-sm text-muted-foreground">
                      {getNextLevelPoints() - getCurrentLevelPoints()} XP remaining
                    </p>
                  </CardContent>
                </Card>

                {/* Stats Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4 text-center">
                      <TrendingUp className="h-6 w-6 text-green-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold">{userStats.totalPoints}</p>
                      <p className="text-sm text-muted-foreground">Total Points</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <Zap className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold">{userStats.streakDays}</p>
                      <p className="text-sm text-muted-foreground">Day Streak</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <CheckCircle className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold">{userStats.completedAssignments}</p>
                      <p className="text-sm text-muted-foreground">Assignments</p>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4 text-center">
                      <Clock className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                      <p className="text-2xl font-bold">{userStats.studyHours}h</p>
                      <p className="text-sm text-muted-foreground">Study Time</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Recent Achievements */}
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Achievements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {achievements.filter(a => a.unlocked).slice(0, 3).map((achievement) => (
                        <div key={achievement.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                          <div className="text-2xl">{achievement.icon}</div>
                          <div className="flex-1">
                            <h4 className="font-medium">{achievement.title}</h4>
                            <p className="text-sm text-muted-foreground">{achievement.description}</p>
                          </div>
                          <Badge className={getRarityColor(achievement.rarity)}>
                            +{achievement.points} XP
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Rank Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Medal className="h-5 w-5 text-bronze" />
                      Your Rank
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-3xl font-bold text-orange-600">#{userStats.rank}</p>
                    <p className="text-sm text-muted-foreground">
                      out of {userStats.totalUsers} students
                    </p>
                  </CardContent>
                </Card>

                {/* Next Achievement */}
                <Card>
                  <CardHeader>
                    <CardTitle>Next Achievement</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      const nextAchievement = achievements.find(a => !a.unlocked && a.progress !== undefined);
                      if (!nextAchievement) return <p className="text-muted-foreground">All achievements unlocked!</p>;
                      
                      return (
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <span className="text-xl">{nextAchievement.icon}</span>
                            <div>
                              <h4 className="font-medium">{nextAchievement.title}</h4>
                              <p className="text-sm text-muted-foreground">{nextAchievement.description}</p>
                            </div>
                          </div>
                          <div>
                            <div className="flex justify-between text-sm mb-1">
                              <span>Progress</span>
                              <span>{nextAchievement.progress}/{nextAchievement.maxProgress}</span>
                            </div>
                            <Progress value={(nextAchievement.progress! / nextAchievement.maxProgress!) * 100} />
                          </div>
                        </div>
                      );
                    })()}
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          {/* Achievements Tab */}
          <TabsContent value="achievements" className="mt-6">
            <div className="mb-4">
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" onClick={() => setActiveTab("achievements")}>
                  All
                </Button>
                <Button variant="outline" size="sm">Unlocked</Button>
                <Button variant="outline" size="sm">Locked</Button>
                <Button variant="outline" size="sm">Academic</Button>
                <Button variant="outline" size="sm">Social</Button>
                <Button variant="outline" size="sm">Consistency</Button>
                <Button variant="outline" size="sm">Special</Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.map((achievement) => (
                <Card key={achievement.id} className={`relative ${achievement.unlocked ? "" : "opacity-75"}`}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="text-2xl">{achievement.icon}</div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getRarityColor(achievement.rarity)}>
                          {achievement.rarity}
                        </Badge>
                        {!achievement.unlocked && <Lock className="h-4 w-4 text-muted-foreground" />}
                      </div>
                    </div>
                    
                    <h3 className="font-semibold mb-1">{achievement.title}</h3>
                    <p className="text-sm text-muted-foreground mb-3">{achievement.description}</p>
                    
                    {achievement.progress !== undefined && achievement.maxProgress && (
                      <div className="mb-3">
                        <div className="flex justify-between text-sm mb-1">
                          <span>Progress</span>
                          <span>{achievement.progress}/{achievement.maxProgress}</span>
                        </div>
                        <Progress value={(achievement.progress / achievement.maxProgress) * 100} />
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        {getCategoryIcon(achievement.category)}
                        <span className="text-sm text-muted-foreground capitalize">{achievement.category}</span>
                      </div>
                      <Badge variant="secondary">+{achievement.points} XP</Badge>
                    </div>
                    
                    {achievement.unlocked && achievement.unlockedAt && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Unlocked {achievement.unlockedAt.toLocaleDateString()}
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Leaderboard Tab */}
          <TabsContent value="leaderboard" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Global Leaderboard</CardTitle>
                <CardDescription>Top students by total points earned</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {leaderboard.map((entry) => (
                    <div 
                      key={entry.rank} 
                      className={`flex items-center gap-3 p-3 rounded-lg ${
                        entry.name.includes(user?.firstName || '') ? 'bg-blue-50 border border-blue-200' : 'bg-muted/50'
                      }`}
                    >
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-bold">
                        {entry.rank}
                      </div>
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={entry.avatar} />
                        <AvatarFallback>{entry.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <p className="font-medium">{entry.name}</p>
                        <p className="text-sm text-muted-foreground">{entry.points} points</p>
                      </div>
                      {entry.rank <= 3 && (
                        <Trophy className={`h-5 w-5 ${
                          entry.rank === 1 ? 'text-yellow-500' : 
                          entry.rank === 2 ? 'text-gray-400' : 'text-orange-600'
                        }`} />
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Challenges Tab */}
          <TabsContent value="challenges" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Weekly Challenge</CardTitle>
                  <CardDescription>Complete 5 assignments this week</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Progress value={60} />
                    <div className="flex justify-between text-sm">
                      <span>3/5 assignments completed</span>
                      <span>2 days left</span>
                    </div>
                    <Badge className="bg-green-100 text-green-800">+200 XP Reward</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Monthly Challenge</CardTitle>
                  <CardDescription>Study for 50 hours this month</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Progress value={75} />
                    <div className="flex justify-between text-sm">
                      <span>37.5/50 hours completed</span>
                      <span>12 days left</span>
                    </div>
                    <Badge className="bg-purple-100 text-purple-800">+500 XP Reward</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Achievements;
