import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { useQuestion } from "@/hooks/useForum";
import DashboardLayout from "@/components/layout/DashboardLayout";
import {
  ArrowUp,
  ArrowDown,
  MessageSquare,
  Tag,
  Calendar,
  User,
  CheckCircle,
  Edit,
  Trash2,
  Reply,
  Send,
  ArrowLeft,
  Clock,
  Eye,
  Loader2,
  Crown
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

// Interfaces are now imported from the API service

const QuestionDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { question, loading, error, createAnswer, acceptAnswer, voteAnswer, voteQuestion } = useQuestion(id!);

  const [answerContent, setAnswerContent] = useState("");
  const [isSubmittingAnswer, setIsSubmittingAnswer] = useState(false);

  const handleSubmitAnswer = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!answerContent.trim()) {
      toast.error("Veuillez saisir une réponse");
      return;
    }

    if (answerContent.length < 10) {
      toast.error("La réponse doit contenir au moins 10 caractères");
      return;
    }

    setIsSubmittingAnswer(true);

    try {
      await createAnswer(answerContent);
      setAnswerContent("");
      toast.success("Réponse ajoutée avec succès!");
    } catch (error: any) {
      toast.error(error.message || "Erreur lors de l'ajout de la réponse");
    } finally {
      setIsSubmittingAnswer(false);
    }
  };

  const handleVoteQuestion = async (voteType: 1 | -1) => {
    try {
      await voteQuestion(voteType);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  const handleVoteAnswer = async (answerId: string, voteType: 1 | -1) => {
    try {
      await voteAnswer(answerId, voteType);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  const handleAcceptAnswer = async (answerId: string) => {
    try {
      await acceptAnswer(answerId);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds} secondes`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} heures`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} jours`;

    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto max-w-4xl">
          <div className="flex items-center justify-center min-h-[400px]">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Chargement de la question...</span>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !question) {
    return (
      <DashboardLayout>
        <div className="container mx-auto max-w-4xl">
          <div className="flex flex-col items-center justify-center min-h-[400px]">
            <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
            <h2 className="text-xl font-semibold mb-2">Question non trouvée</h2>
            <p className="text-muted-foreground mb-4">
              {error || "Cette question n'existe pas ou a été supprimée."}
            </p>
            <Button onClick={() => navigate("/dashboard/forum")}>
              Retour au Forum
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate("/dashboard/forum")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Retour au Forum
          </Button>
        </div>

        {/* Question */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-2xl mb-2">{question.title}</CardTitle>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={question.author.avatar} />
                      <AvatarFallback>{question.author.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <span>{question.author.name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>Il y a {formatRelativeTime(question.created_at)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    <span>{question.views} vues</span>
                  </div>
                </div>
              </div>

              {question.solved && (
                <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Résolu
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-start gap-4">
              {/* Vote buttons */}
              <div className="flex flex-col items-center space-y-1">
                <button
                  className="p-2 rounded hover:bg-muted"
                  onClick={() => handleVoteQuestion(1)}
                  disabled={loading}
                >
                  <ArrowUp className={`h-5 w-5 ${question.user_vote === 1 ? 'text-green-600 fill-current' : ''}`} />
                </button>
                <span className="text-lg font-medium py-1">{question.votes}</span>
                <button
                  className="p-2 rounded hover:bg-muted"
                  onClick={() => handleVoteQuestion(-1)}
                  disabled={loading}
                >
                  <ArrowDown className={`h-5 w-5 ${question.user_vote === -1 ? 'text-red-600 fill-current' : ''}`} />
                </button>
              </div>

              {/* Content */}
              <div className="flex-1">
                <div className="prose prose-sm max-w-none mb-4">
                  <p className="whitespace-pre-wrap">{question.content}</p>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {question.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Answers */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">
            {question.answers.length} {question.answers.length === 1 ? 'Réponse' : 'Réponses'}
          </h2>

          {question.answers.length > 0 ? (
            <div className="space-y-4">
              {question.answers.map((answer) => (
                <Card key={answer.id} className={answer.is_accepted ? "border-green-200 bg-green-50/50" : ""}>
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      {/* Vote buttons */}
                      <div className="flex flex-col items-center space-y-1">
                        <button
                          className="p-2 rounded hover:bg-muted"
                          onClick={() => handleVoteAnswer(answer.id, 1)}
                          disabled={loading}
                        >
                          <ArrowUp className={`h-4 w-4 ${answer.user_vote === 1 ? 'text-green-600 fill-current' : ''}`} />
                        </button>
                        <span className="text-sm font-medium py-1">{answer.votes}</span>
                        <button
                          className="p-2 rounded hover:bg-muted"
                          onClick={() => handleVoteAnswer(answer.id, -1)}
                          disabled={loading}
                        >
                          <ArrowDown className={`h-4 w-4 ${answer.user_vote === -1 ? 'text-red-600 fill-current' : ''}`} />
                        </button>

                        {answer.is_accepted && (
                          <div className="mt-2">
                            <CheckCircle className="h-6 w-6 text-green-600" />
                          </div>
                        )}
                      </div>

                      {/* Content */}
                      <div className="flex-1">
                        <div className="prose prose-sm max-w-none mb-4">
                          <p className="whitespace-pre-wrap">{answer.content}</p>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={answer.author.avatar} />
                                <AvatarFallback>{answer.author.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <span>{answer.author.name}</span>
                            </div>
                            <span>Il y a {formatRelativeTime(answer.created_at)}</span>
                          </div>

                          {/* Accept answer button (only for question author) */}
                          {user?.id === question.author.id && !question.solved && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAcceptAnswer(answer.id)}
                              className="text-green-600 border-green-200 hover:bg-green-50"
                            >
                              <Crown className="h-4 w-4 mr-1" />
                              Accepter cette réponse
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Aucune réponse pour le moment</h3>
                  <p className="text-muted-foreground">
                    Soyez le premier à aider en répondant à cette question !
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Add Answer Form */}
        {user && (
          <Card>
            <CardHeader>
              <CardTitle>Votre Réponse</CardTitle>
              <CardDescription>
                Aidez la communauté en partageant vos connaissances
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmitAnswer} className="space-y-4">
                <Textarea
                  placeholder="Écrivez votre réponse ici... Soyez détaillé et constructif."
                  value={answerContent}
                  onChange={(e) => setAnswerContent(e.target.value)}
                  className="min-h-[120px]"
                />
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    {answerContent.length} caractères (minimum 10)
                  </p>
                  <Button type="submit" disabled={isSubmittingAnswer || answerContent.length < 10}>
                    {isSubmittingAnswer ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Publication...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-4 w-4" />
                        Publier la Réponse
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
};

export default QuestionDetail;
