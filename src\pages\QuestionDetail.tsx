import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { 
  ArrowUp, 
  ArrowDown, 
  MessageSquare, 
  Tag, 
  Calendar, 
  User,
  CheckCircle,
  Edit,
  Trash2,
  Reply,
  Send,
  ArrowLeft
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

interface Answer {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  votes: number;
  createdAt: string;
  isAccepted: boolean;
}

interface QuestionData {
  id: string;
  title: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  tags: string[];
  votes: number;
  views: number;
  createdAt: string;
  solved: boolean;
  answers: Answer[];
}

const QuestionDetail = () => {
  const { questionId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [newAnswer, setNewAnswer] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock question data - in a real app, this would be fetched based on questionId
  const [question] = useState<QuestionData>({
    id: questionId || "q1",
    title: "How to calculate derivatives in multivariable calculus?",
    content: `I'm struggling with understanding the concept of partial derivatives in multivariable calculus. 

Specifically, I'm having trouble with:
1. Understanding when to use ∂ vs d notation
2. Computing partial derivatives of complex functions
3. The geometric interpretation of partial derivatives

Here's an example I'm working on:
f(x,y) = x²y + 3xy² - 2y³

I need to find ∂f/∂x and ∂f/∂y, but I'm not sure about the process. Can someone explain the step-by-step approach?

Any help would be greatly appreciated!`,
    author: {
      id: "u1",
      name: "Alex Johnson",
      role: "student"
    },
    tags: ["math", "calculus", "derivatives", "multivariable"],
    votes: 12,
    views: 156,
    createdAt: "2025-05-10T14:30:00Z",
    solved: true,
    answers: [
      {
        id: "a1",
        content: `Great question! Let me break down partial derivatives for you:

**1. Notation:**
- ∂ (partial) is used when you have multiple variables
- d is used for single-variable functions

**2. Process for your example f(x,y) = x²y + 3xy² - 2y³:**

**Finding ∂f/∂x:**
- Treat y as a constant
- ∂f/∂x = 2xy + 3y² + 0 = 2xy + 3y²

**Finding ∂f/∂y:**
- Treat x as a constant  
- ∂f/∂y = x² + 6xy - 6y²

**3. Geometric interpretation:**
- ∂f/∂x gives the slope in the x-direction at any point
- ∂f/∂y gives the slope in the y-direction at any point

Hope this helps! Let me know if you need clarification on any step.`,
        author: {
          id: "u2",
          name: "Dr. Sarah Miller",
          avatar: "/placeholder.svg",
          role: "teacher"
        },
        votes: 15,
        createdAt: "2025-05-10T15:45:00Z",
        isAccepted: true
      },
      {
        id: "a2",
        content: `To add to Dr. Miller's excellent explanation, here are some helpful tips:

1. **Practice with simpler functions first** - start with f(x,y) = x² + y²
2. **Use the chain rule** when dealing with composite functions
3. **Visualize with 3D graphs** - tools like Desmos 3D can help you see the geometric meaning

For your specific example, you can verify your answers by checking that the mixed partial derivatives are equal (Clairaut's theorem): ∂²f/∂x∂y = ∂²f/∂y∂x`,
        author: {
          id: "u3",
          name: "Maria Garcia",
          avatar: "/placeholder.svg",
          role: "student"
        },
        votes: 8,
        createdAt: "2025-05-10T16:20:00Z",
        isAccepted: false
      }
    ]
  });

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    return date.toLocaleDateString();
  };

  const handleVote = (type: 'up' | 'down', targetId: string, targetType: 'question' | 'answer') => {
    toast.success(`${type === 'up' ? 'Upvoted' : 'Downvoted'} ${targetType}!`);
  };

  const handleAcceptAnswer = (answerId: string) => {
    toast.success("Answer marked as accepted!");
  };

  const handleSubmitAnswer = async () => {
    if (!newAnswer.trim()) {
      toast.error("Please enter an answer");
      return;
    }

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Answer submitted successfully!");
      setNewAnswer("");
    } catch (error) {
      toast.error("Failed to submit answer");
    } finally {
      setIsSubmitting(false);
    }
  };

  const isQuestionAuthor = question.author.id === user?.id;

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <Button 
            variant="ghost" 
            onClick={() => navigate("/dashboard/forum")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Forum
          </Button>
          
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-2xl font-bold mb-2">{question.title}</h1>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Asked {formatRelativeTime(question.createdAt)}
                </div>
                <div className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  {question.views} views
                </div>
                {question.solved && (
                  <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                    <CheckCircle className="mr-1 h-3 w-3" />
                    Solved
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Question */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex gap-4">
              {/* Vote buttons */}
              <div className="flex flex-col items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleVote('up', question.id, 'question')}
                  className="p-2"
                >
                  <ArrowUp className="h-5 w-5" />
                </Button>
                <span className="font-medium text-lg">{question.votes}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleVote('down', question.id, 'question')}
                  className="p-2"
                >
                  <ArrowDown className="h-5 w-5" />
                </Button>
              </div>

              {/* Question content */}
              <div className="flex-1">
                <div className="prose max-w-none mb-4">
                  <p className="whitespace-pre-wrap">{question.content}</p>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {question.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                      <Tag className="h-3 w-3" />
                      {tag}
                    </Badge>
                  ))}
                </div>

                {/* Author info */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={question.author.avatar} />
                      <AvatarFallback>{question.author.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium text-sm">{question.author.name}</p>
                      <p className="text-xs text-muted-foreground capitalize">{question.author.role}</p>
                    </div>
                  </div>

                  {isQuestionAuthor && (
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Answers */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">
            {question.answers.length} {question.answers.length === 1 ? 'Answer' : 'Answers'}
          </h2>

          <div className="space-y-4">
            {question.answers.map((answer) => (
              <Card key={answer.id} className={answer.isAccepted ? "border-green-200 bg-green-50/30" : ""}>
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    {/* Vote buttons */}
                    <div className="flex flex-col items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleVote('up', answer.id, 'answer')}
                        className="p-2"
                      >
                        <ArrowUp className="h-5 w-5" />
                      </Button>
                      <span className="font-medium text-lg">{answer.votes}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleVote('down', answer.id, 'answer')}
                        className="p-2"
                      >
                        <ArrowDown className="h-5 w-5" />
                      </Button>
                      
                      {isQuestionAuthor && !answer.isAccepted && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleAcceptAnswer(answer.id)}
                          className="p-2 text-green-600 hover:text-green-700"
                          title="Accept this answer"
                        >
                          <CheckCircle className="h-5 w-5" />
                        </Button>
                      )}
                      
                      {answer.isAccepted && (
                        <div className="p-2 text-green-600" title="Accepted answer">
                          <CheckCircle className="h-5 w-5 fill-current" />
                        </div>
                      )}
                    </div>

                    {/* Answer content */}
                    <div className="flex-1">
                      <div className="prose max-w-none mb-4">
                        <p className="whitespace-pre-wrap">{answer.content}</p>
                      </div>

                      {/* Author info */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={answer.author.avatar} />
                            <AvatarFallback>{answer.author.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-sm">{answer.author.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {formatRelativeTime(answer.createdAt)} • {answer.author.role}
                            </p>
                          </div>
                        </div>

                        <Button variant="ghost" size="sm">
                          <Reply className="mr-2 h-4 w-4" />
                          Reply
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Add Answer */}
        <Card>
          <CardHeader>
            <CardTitle>Your Answer</CardTitle>
            <CardDescription>
              Share your knowledge and help the community
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Textarea
                placeholder="Write your answer here... Be detailed and helpful."
                value={newAnswer}
                onChange={(e) => setNewAnswer(e.target.value)}
                className="min-h-32"
              />
              <div className="flex justify-between items-center">
                <p className="text-sm text-muted-foreground">
                  {newAnswer.length}/1000 characters
                </p>
                <Button onClick={handleSubmitAnswer} disabled={isSubmitting || !newAnswer.trim()}>
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Send className="mr-2 h-4 w-4" />
                      Post Answer
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default QuestionDetail;
