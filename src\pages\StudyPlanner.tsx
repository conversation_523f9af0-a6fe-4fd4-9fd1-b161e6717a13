import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { 
  Calendar, 
  Clock, 
  Plus, 
  Target, 
  TrendingUp, 
  BookOpen,
  CheckCircle,
  AlertCircle,
  Edit,
  Trash2,
  Play,
  Pause
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, Di<PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";

interface StudyTask {
  id: string;
  title: string;
  description: string;
  subject: string;
  priority: "low" | "medium" | "high";
  estimatedTime: number; // in minutes
  actualTime?: number;
  dueDate: Date;
  completed: boolean;
  createdAt: Date;
}

interface StudySession {
  id: string;
  taskId: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in minutes
  notes?: string;
}

const StudyPlanner = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("today");
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [activeSession, setActiveSession] = useState<string | null>(null);
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);

  const [newTask, setNewTask] = useState({
    title: "",
    description: "",
    subject: "",
    priority: "medium" as const,
    estimatedTime: 60,
    dueDate: new Date()
  });

  // Mock data
  const [tasks, setTasks] = useState<StudyTask[]>([
    {
      id: "1",
      title: "Review Calculus Chapter 8",
      description: "Go through derivatives and practice problems 1-15",
      subject: "Mathematics",
      priority: "high",
      estimatedTime: 120,
      actualTime: 90,
      dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      completed: false,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: "2",
      title: "Complete React Assignment",
      description: "Finish the todo app with authentication",
      subject: "Computer Science",
      priority: "high",
      estimatedTime: 180,
      dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      completed: false,
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
    },
    {
      id: "3",
      title: "Read Physics Chapter 12",
      description: "Quantum mechanics introduction",
      subject: "Physics",
      priority: "medium",
      estimatedTime: 90,
      actualTime: 85,
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      completed: true,
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    }
  ]);

  const [sessions] = useState<StudySession[]>([
    {
      id: "1",
      taskId: "3",
      startTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
      endTime: new Date(Date.now() - 60 * 60 * 1000),
      duration: 85,
      notes: "Good progress on quantum concepts"
    }
  ]);

  const subjects = ["Mathematics", "Computer Science", "Physics", "Chemistry", "English", "History"];

  const getTodayTasks = () => {
    const today = new Date();
    return tasks.filter(task => {
      const taskDate = new Date(task.dueDate);
      return taskDate.toDateString() === today.toDateString();
    });
  };

  const getUpcomingTasks = () => {
    const today = new Date();
    return tasks.filter(task => {
      const taskDate = new Date(task.dueDate);
      return taskDate > today;
    }).sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
  };

  const getOverdueTasks = () => {
    const today = new Date();
    return tasks.filter(task => {
      const taskDate = new Date(task.dueDate);
      return taskDate < today && !task.completed;
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "text-red-600 bg-red-50 border-red-200";
      case "medium": return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "low": return "text-green-600 bg-green-50 border-green-200";
      default: return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const handleAddTask = () => {
    if (!newTask.title.trim()) {
      toast.error("Please enter a task title");
      return;
    }

    const task: StudyTask = {
      id: Date.now().toString(),
      ...newTask,
      completed: false,
      createdAt: new Date()
    };

    setTasks(prev => [...prev, task]);
    setNewTask({
      title: "",
      description: "",
      subject: "",
      priority: "medium",
      estimatedTime: 60,
      dueDate: new Date()
    });
    setIsAddingTask(false);
    toast.success("Task added successfully!");
  };

  const toggleTaskComplete = (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, completed: !task.completed } : task
    ));
    toast.success("Task updated!");
  };

  const startStudySession = (taskId: string) => {
    setActiveSession(taskId);
    setSessionStartTime(new Date());
    toast.success("Study session started!");
  };

  const endStudySession = () => {
    if (activeSession && sessionStartTime) {
      const duration = Math.floor((new Date().getTime() - sessionStartTime.getTime()) / (1000 * 60));
      // In a real app, save the session
      toast.success(`Study session completed! Duration: ${formatDuration(duration)}`);
    }
    setActiveSession(null);
    setSessionStartTime(null);
  };

  const getWeeklyProgress = () => {
    const completedTasks = tasks.filter(task => task.completed).length;
    const totalTasks = tasks.length;
    return totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
  };

  const getTotalStudyTime = () => {
    return sessions.reduce((total, session) => total + session.duration, 0);
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-6xl">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Target className="h-6 w-6" />
              Study Planner
            </h1>
            <p className="text-muted-foreground">
              Organize your study schedule and track your progress
            </p>
          </div>
          <Dialog open={isAddingTask} onOpenChange={setIsAddingTask}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Task
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Study Task</DialogTitle>
                <DialogDescription>
                  Create a new task for your study schedule
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Task Title</Label>
                  <Input
                    id="title"
                    value={newTask.title}
                    onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="e.g., Review Calculus Chapter 8"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={newTask.description}
                    onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Detailed description of the task..."
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="subject">Subject</Label>
                    <Select value={newTask.subject} onValueChange={(value) => setNewTask(prev => ({ ...prev, subject: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select subject" />
                      </SelectTrigger>
                      <SelectContent>
                        {subjects.map(subject => (
                          <SelectItem key={subject} value={subject}>{subject}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="priority">Priority</Label>
                    <Select value={newTask.priority} onValueChange={(value: any) => setNewTask(prev => ({ ...prev, priority: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="estimatedTime">Estimated Time (minutes)</Label>
                    <Input
                      id="estimatedTime"
                      type="number"
                      value={newTask.estimatedTime}
                      onChange={(e) => setNewTask(prev => ({ ...prev, estimatedTime: parseInt(e.target.value) || 60 }))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="dueDate">Due Date</Label>
                    <Input
                      id="dueDate"
                      type="date"
                      value={newTask.dueDate.toISOString().split('T')[0]}
                      onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: new Date(e.target.value) }))}
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsAddingTask(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddTask}>
                    Add Task
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Weekly Progress</p>
                  <p className="text-2xl font-bold">{getWeeklyProgress()}%</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Study Time</p>
                  <p className="text-2xl font-bold">{formatDuration(getTotalStudyTime())}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Completed Tasks</p>
                  <p className="text-2xl font-bold">{tasks.filter(t => t.completed).length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Overdue Tasks</p>
                  <p className="text-2xl font-bold">{getOverdueTasks().length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Active Session */}
        {activeSession && (
          <Card className="mb-6 border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Play className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium">Study Session Active</p>
                    <p className="text-sm text-muted-foreground">
                      {tasks.find(t => t.id === activeSession)?.title}
                    </p>
                  </div>
                </div>
                <Button onClick={endStudySession} variant="outline">
                  <Pause className="mr-2 h-4 w-4" />
                  End Session
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tasks Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="today">Today ({getTodayTasks().length})</TabsTrigger>
            <TabsTrigger value="upcoming">Upcoming ({getUpcomingTasks().length})</TabsTrigger>
            <TabsTrigger value="overdue">Overdue ({getOverdueTasks().length})</TabsTrigger>
            <TabsTrigger value="completed">Completed ({tasks.filter(t => t.completed).length})</TabsTrigger>
          </TabsList>

          <TabsContent value="today" className="mt-6">
            <TaskList 
              tasks={getTodayTasks()} 
              onToggleComplete={toggleTaskComplete}
              onStartSession={startStudySession}
              activeSession={activeSession}
            />
          </TabsContent>

          <TabsContent value="upcoming" className="mt-6">
            <TaskList 
              tasks={getUpcomingTasks()} 
              onToggleComplete={toggleTaskComplete}
              onStartSession={startStudySession}
              activeSession={activeSession}
            />
          </TabsContent>

          <TabsContent value="overdue" className="mt-6">
            <TaskList 
              tasks={getOverdueTasks()} 
              onToggleComplete={toggleTaskComplete}
              onStartSession={startStudySession}
              activeSession={activeSession}
            />
          </TabsContent>

          <TabsContent value="completed" className="mt-6">
            <TaskList 
              tasks={tasks.filter(t => t.completed)} 
              onToggleComplete={toggleTaskComplete}
              onStartSession={startStudySession}
              activeSession={activeSession}
            />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

interface TaskListProps {
  tasks: StudyTask[];
  onToggleComplete: (taskId: string) => void;
  onStartSession: (taskId: string) => void;
  activeSession: string | null;
}

const TaskList = ({ tasks, onToggleComplete, onStartSession, activeSession }: TaskListProps) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "text-red-600 bg-red-50 border-red-200";
      case "medium": return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "low": return "text-green-600 bg-green-50 border-green-200";
      default: return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  if (tasks.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tasks found</h3>
          <p className="text-muted-foreground">Add some study tasks to get started!</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {tasks.map((task) => (
        <Card key={task.id} className={task.completed ? "opacity-75" : ""}>
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1">
                <input
                  type="checkbox"
                  checked={task.completed}
                  onChange={() => onToggleComplete(task.id)}
                  className="mt-1"
                />
                <div className="flex-1">
                  <h3 className={`font-medium ${task.completed ? "line-through text-muted-foreground" : ""}`}>
                    {task.title}
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                    <Badge variant="secondary">{task.subject}</Badge>
                    <span className="text-xs text-muted-foreground flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatDuration(task.estimatedTime)}
                    </span>
                    <span className="text-xs text-muted-foreground flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {task.dueDate.toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {!task.completed && activeSession !== task.id && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onStartSession(task.id)}
                  >
                    <Play className="h-4 w-4" />
                  </Button>
                )}
                {activeSession === task.id && (
                  <Badge variant="outline" className="text-blue-600 border-blue-600">
                    Active
                  </Badge>
                )}
                <Button variant="ghost" size="sm">
                  <Edit className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm">
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default StudyPlanner;
