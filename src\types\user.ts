
export type UserRole = "student" | "teacher" | "admin";

// Updated User interface to match Django backend
export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone?: string;
  role: UserRole;
  profile_picture?: string;
  bio?: string;
  student_id?: string;
  department?: string;
  year_of_study?: number;
  gpa?: number;
  is_2fa_enabled: boolean;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  theme_preference: 'light' | 'dark' | 'system';
  initials: string;
}

// Legacy interface for backward compatibility
export interface LegacyUser {
  id: string;
  username: string;
  email: string;
  phone?: string;
  role: UserRole;
  firstName: string;
  lastName: string;
  createdAt: Date;
  profilePicture?: string;
  is2FAEnabled: boolean;
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
}

// API interfaces
export interface LoginCredentials {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: UserRole;
}
