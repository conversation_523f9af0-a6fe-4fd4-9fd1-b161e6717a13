import { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import { Trophy, Star, Award, Crown, Zap, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface BadgeUnlockAnimationProps {
  isVisible: boolean;
  badge: {
    id: string;
    title: string;
    description: string;
    icon: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    points: number;
  };
  onClose: () => void;
}

const BadgeUnlockAnimation = ({ isVisible, badge, onClose }: BadgeUnlockAnimationProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const badgeRef = useRef<HTMLDivElement>(null);
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    if (!isVisible || !containerRef.current || !badgeRef.current) return;

    const container = containerRef.current;
    const badgeElement = badgeRef.current;

    // Create confetti particles
    const confettiCount = 30;
    const confettiElements: HTMLElement[] = [];

    for (let i = 0; i < confettiCount; i++) {
      const confetti = document.createElement('div');
      confetti.style.cssText = `
        position: absolute;
        width: 8px;
        height: 8px;
        background: hsl(${Math.random() * 360}, 80%, 60%);
        border-radius: 50%;
        pointer-events: none;
        top: 50%;
        left: 50%;
      `;
      container.appendChild(confetti);
      confettiElements.push(confetti);
    }

    // Animation timeline
    const tl = gsap.timeline();

    // Initial state
    gsap.set(container, { opacity: 0 });
    gsap.set(badgeElement, { scale: 0, rotation: -180, y: 50 });
    gsap.set(confettiElements, { scale: 0, opacity: 0 });

    // Entrance animation
    tl.to(container, {
      opacity: 1,
      duration: 0.3,
      ease: "power2.out"
    })
    .to(badgeElement, {
      scale: 1.2,
      rotation: 0,
      y: 0,
      duration: 0.8,
      ease: "back.out(1.7)"
    })
    .to(badgeElement, {
      scale: 1,
      duration: 0.3,
      ease: "power2.out"
    }, "-=0.2");

    // Confetti explosion
    confettiElements.forEach((confetti, index) => {
      const angle = (index / confettiCount) * Math.PI * 2;
      const distance = 150 + Math.random() * 100;
      const x = Math.cos(angle) * distance;
      const y = Math.sin(angle) * distance;

      tl.to(confetti, {
        x,
        y,
        scale: 1,
        opacity: 1,
        duration: 0.6,
        ease: "power2.out"
      }, "-=0.5")
      .to(confetti, {
        y: y + 100,
        opacity: 0,
        duration: 1,
        ease: "power2.in"
      }, "-=0.3");
    });

    // Badge glow effect
    tl.to(badgeElement, {
      boxShadow: "0 0 30px rgba(255, 215, 0, 0.8)",
      duration: 0.5,
      yoyo: true,
      repeat: 3,
      ease: "power2.inOut"
    }, "-=1");

    // Cleanup function
    return () => {
      tl.kill();
      confettiElements.forEach(el => el.remove());
    };
  }, [isVisible]);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'from-gray-400 to-gray-600';
      case 'rare': return 'from-blue-400 to-blue-600';
      case 'epic': return 'from-purple-400 to-purple-600';
      case 'legendary': return 'from-yellow-400 to-yellow-600';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case 'common': return <Star className="h-6 w-6" />;
      case 'rare': return <Award className="h-6 w-6" />;
      case 'epic': return <Trophy className="h-6 w-6" />;
      case 'legendary': return <Crown className="h-6 w-6" />;
      default: return <Star className="h-6 w-6" />;
    }
  };

  if (!isVisible) return null;

  return (
    <div 
      ref={containerRef}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
      onClick={onClose}
    >
      <Card 
        ref={badgeRef}
        className="relative max-w-md mx-4 overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className={`absolute inset-0 bg-gradient-to-br ${getRarityColor(badge.rarity)} opacity-10`} />
        
        <CardContent className="p-8 text-center relative">
          {/* Badge Icon */}
          <div className="relative mb-6">
            <div className={`w-24 h-24 mx-auto rounded-full bg-gradient-to-br ${getRarityColor(badge.rarity)} flex items-center justify-center text-white text-4xl shadow-lg`}>
              {badge.icon}
            </div>
            <div className="absolute -top-2 -right-2">
              {getRarityIcon(badge.rarity)}
            </div>
          </div>

          {/* Badge Info */}
          <div className="space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-foreground mb-2">
                🎉 Badge Unlocked!
              </h2>
              <h3 className="text-xl font-semibold text-foreground">
                {badge.title}
              </h3>
            </div>

            <p className="text-muted-foreground">
              {badge.description}
            </p>

            <div className="flex items-center justify-center gap-4">
              <div className={`px-3 py-1 rounded-full bg-gradient-to-r ${getRarityColor(badge.rarity)} text-white text-sm font-medium`}>
                {badge.rarity.toUpperCase()}
              </div>
              <div className="flex items-center gap-1 text-yellow-600">
                <Zap className="h-4 w-4" />
                <span className="font-bold">+{badge.points} XP</span>
              </div>
            </div>

            <Button 
              onClick={onClose}
              className="w-full mt-6"
              size="lg"
            >
              Awesome! 🚀
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BadgeUnlockAnimation;
