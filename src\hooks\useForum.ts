import { useState, useEffect } from 'react';
import { apiService, ForumQuestion, ForumAnswer } from '@/services/api';
import { toast } from 'sonner';

export interface ForumFilters {
  search?: string;
  tags?: string[];
  author?: string;
  solved?: boolean;
  my_questions?: boolean;
  unanswered?: boolean;
  ordering?: 'newest' | 'oldest' | 'votes' | 'activity';
}

export const useForum = () => {
  const [questions, setQuestions] = useState<ForumQuestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);

  const fetchQuestions = async (filters?: ForumFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.getQuestions(filters);
      setQuestions(response.results);
      setTotalCount(response.count);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch questions');
      toast.error('Failed to load questions');
    } finally {
      setLoading(false);
    }
  };

  const createQuestion = async (questionData: {
    title: string;
    content: string;
    tags: string[];
  }) => {
    try {
      const newQuestion = await apiService.createQuestion(questionData);
      setQuestions(prev => [newQuestion, ...prev]);
      toast.success('Question created successfully!');
      return newQuestion;
    } catch (err: any) {
      toast.error(err.message || 'Failed to create question');
      throw err;
    }
  };

  const vote = async (
    contentType: 'question' | 'answer',
    objectId: string,
    voteType: 1 | -1
  ) => {
    try {
      const response = await apiService.vote({
        content_type: contentType,
        object_id: objectId,
        vote_type: voteType,
      });

      // Update local state
      if (contentType === 'question') {
        setQuestions(prev =>
          prev.map(q =>
            q.id === objectId
              ? { ...q, votes: response.vote_count, user_vote: response.user_vote }
              : q
          )
        );
      }

      return response;
    } catch (err: any) {
      toast.error(err.message || 'Failed to vote');
      throw err;
    }
  };

  return {
    questions,
    loading,
    error,
    totalCount,
    fetchQuestions,
    createQuestion,
    vote,
  };
};

export const useQuestion = (questionId: string) => {
  const [question, setQuestion] = useState<(ForumQuestion & { answers: ForumAnswer[] }) | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchQuestion = async () => {
    if (!questionId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.getQuestion(questionId);
      setQuestion(response);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch question');
      toast.error('Failed to load question');
    } finally {
      setLoading(false);
    }
  };

  const createAnswer = async (content: string) => {
    if (!questionId) return;
    
    try {
      const newAnswer = await apiService.createAnswer(questionId, content);
      setQuestion(prev => prev ? {
        ...prev,
        answers: [...prev.answers, newAnswer]
      } : null);
      toast.success('Answer posted successfully!');
      return newAnswer;
    } catch (err: any) {
      toast.error(err.message || 'Failed to post answer');
      throw err;
    }
  };

  const acceptAnswer = async (answerId: string) => {
    try {
      const response = await apiService.acceptAnswer(answerId);
      
      // Update local state
      setQuestion(prev => prev ? {
        ...prev,
        is_solved: response.is_accepted,
        answers: prev.answers.map(answer => ({
          ...answer,
          is_accepted: answer.id === answerId ? response.is_accepted : false
        }))
      } : null);
      
      toast.success(response.message);
      return response;
    } catch (err: any) {
      toast.error(err.message || 'Failed to accept answer');
      throw err;
    }
  };

  const voteAnswer = async (answerId: string, voteType: 1 | -1) => {
    try {
      const response = await apiService.vote({
        content_type: 'answer',
        object_id: answerId,
        vote_type: voteType,
      });

      // Update local state
      setQuestion(prev => prev ? {
        ...prev,
        answers: prev.answers.map(answer =>
          answer.id === answerId
            ? { ...answer, votes: response.vote_count, user_vote: response.user_vote }
            : answer
        )
      } : null);

      return response;
    } catch (err: any) {
      toast.error(err.message || 'Failed to vote');
      throw err;
    }
  };

  const voteQuestion = async (voteType: 1 | -1) => {
    if (!question) return;
    
    try {
      const response = await apiService.vote({
        content_type: 'question',
        object_id: question.id,
        vote_type: voteType,
      });

      // Update local state
      setQuestion(prev => prev ? {
        ...prev,
        votes: response.vote_count,
        user_vote: response.user_vote
      } : null);

      return response;
    } catch (err: any) {
      toast.error(err.message || 'Failed to vote');
      throw err;
    }
  };

  useEffect(() => {
    fetchQuestion();
  }, [questionId]);

  return {
    question,
    loading,
    error,
    fetchQuestion,
    createAnswer,
    acceptAnswer,
    voteAnswer,
    voteQuestion,
  };
};

export const useForumStats = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.getForumStats();
      setStats(response);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch forum stats');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    loading,
    error,
    fetchStats,
  };
};

export const useTags = () => {
  const [tags, setTags] = useState<Array<{ id: number; name: string; question_count: number }>>([]);
  const [popularTags, setPopularTags] = useState<Array<{ id: number; name: string; question_count: number }>>([]);
  const [loading, setLoading] = useState(false);

  const fetchTags = async () => {
    setLoading(true);
    try {
      const [allTags, popular] = await Promise.all([
        apiService.getTags(),
        apiService.getPopularTags(),
      ]);
      setTags(allTags);
      setPopularTags(popular);
    } catch (err: any) {
      toast.error('Failed to load tags');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTags();
  }, []);

  return {
    tags,
    popularTags,
    loading,
    fetchTags,
  };
};
