/**
 * API Service for Campus Connect
 * Handles all communication with Django backend
 */

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  password_confirm: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: 'student' | 'teacher' | 'admin';
}

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone?: string;
  role: 'student' | 'teacher' | 'admin';
  profile_picture?: string;
  bio?: string;
  student_id?: string;
  department?: string;
  year_of_study?: number;
  gpa?: number;
  is_2fa_enabled: boolean;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  theme_preference: 'light' | 'dark' | 'system';
  initials: string;
}

export interface ForumQuestion {
  id: string;
  title: string;
  content: string;
  author: {
    id: number;
    name: string;
    avatar?: string;
    role: string;
  };
  tags: string[];
  votes: number;
  answers: number;
  views: number;
  created_at: string;
  solved: boolean;
  user_vote?: number;
}

export interface ForumAnswer {
  id: string;
  content: string;
  author: {
    id: number;
    name: string;
    avatar?: string;
    role: string;
  };
  votes: number;
  is_accepted: boolean;
  created_at: string;
  updated_at: string;
  user_vote?: number;
}

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('access_token');
  }

  private async request<T = any>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers as Record<string, string>,
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (response.status === 401) {
        // Token expired, try to refresh
        const refreshed = await this.refreshToken();
        if (refreshed) {
          headers['Authorization'] = `Bearer ${this.token}`;
          const retryResponse = await fetch(url, {
            ...options,
            headers,
          });
          if (!retryResponse.ok) {
            throw new Error(`API Error: ${retryResponse.status}`);
          }
          return retryResponse.json();
        } else {
          // Refresh failed, redirect to login
          this.logout();
          throw new Error('Authentication failed');
        }
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `API Error: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error('API Request failed:', error);
      throw error;
    }
  }

  private async refreshToken(): Promise<boolean> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) return false;

    try {
      const response = await fetch(`${this.baseURL}/auth/token/refresh/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refresh: refreshToken }),
      });

      if (response.ok) {
        const data = await response.json();
        this.token = data.access;
        localStorage.setItem('access_token', data.access);
        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    return false;
  }

  setToken(token: string) {
    this.token = token;
    localStorage.setItem('access_token', token);
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  // Authentication
  async login(credentials: LoginCredentials): Promise<{ user: User; tokens: any; message: string }> {
    const data = await this.request<{ user: User; tokens: any; message: string }>('/auth/login/', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    this.setToken(data.tokens.access);
    localStorage.setItem('refresh_token', data.tokens.refresh);
    
    return data;
  }

  async register(userData: RegisterData): Promise<{ user: User; tokens: any; message: string }> {
    const data = await this.request<{ user: User; tokens: any; message: string }>('/auth/register/', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    
    this.setToken(data.tokens.access);
    localStorage.setItem('refresh_token', data.tokens.refresh);
    
    return data;
  }

  async logout(): Promise<void> {
    const refreshToken = localStorage.getItem('refresh_token');
    
    try {
      await this.request('/auth/logout/', {
        method: 'POST',
        body: JSON.stringify({ refresh_token: refreshToken }),
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearToken();
    }
  }

  async getProfile(): Promise<User> {
    return this.request<User>('/auth/profile/');
  }

  async updateProfile(profileData: Partial<User>): Promise<User> {
    return this.request<User>('/auth/profile/', {
      method: 'PUT',
      body: JSON.stringify(profileData),
    });
  }

  async changePassword(passwordData: {
    old_password: string;
    new_password: string;
    new_password_confirm: string;
  }): Promise<{ message: string }> {
    return this.request('/auth/change-password/', {
      method: 'POST',
      body: JSON.stringify(passwordData),
    });
  }

  async getDashboardData(): Promise<any> {
    return this.request('/auth/dashboard-data/');
  }

  // Forum API
  async getQuestions(params?: {
    search?: string;
    tags?: string[];
    author?: string;
    solved?: boolean;
    my_questions?: boolean;
    unanswered?: boolean;
    ordering?: string;
  }): Promise<{ results: ForumQuestion[]; count: number }> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      if (params.search) searchParams.append('search', params.search);
      if (params.tags) params.tags.forEach(tag => searchParams.append('tags', tag));
      if (params.author) searchParams.append('author', params.author);
      if (params.solved !== undefined) searchParams.append('solved', params.solved.toString());
      if (params.my_questions) searchParams.append('my_questions', 'true');
      if (params.unanswered) searchParams.append('unanswered', 'true');
      if (params.ordering) searchParams.append('ordering', params.ordering);
    }

    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';
    return this.request(`/forum/questions/${query}`);
  }

  async getQuestion(id: string): Promise<ForumQuestion & { answers: ForumAnswer[] }> {
    return this.request(`/forum/questions/${id}/`);
  }

  async createQuestion(questionData: {
    title: string;
    content: string;
    tags: string[];
  }): Promise<ForumQuestion> {
    return this.request('/forum/questions/', {
      method: 'POST',
      body: JSON.stringify(questionData),
    });
  }

  async createAnswer(questionId: string, content: string): Promise<ForumAnswer> {
    return this.request(`/forum/questions/${questionId}/answers/`, {
      method: 'POST',
      body: JSON.stringify({ content }),
    });
  }

  async vote(voteData: {
    content_type: 'question' | 'answer';
    object_id: string;
    vote_type: 1 | -1;
  }): Promise<{ message: string; vote_count: number; user_vote: number | null }> {
    return this.request('/forum/vote/', {
      method: 'POST',
      body: JSON.stringify(voteData),
    });
  }

  async acceptAnswer(answerId: string): Promise<{ message: string; is_accepted: boolean }> {
    return this.request(`/forum/answers/${answerId}/accept/`, {
      method: 'POST',
    });
  }

  async getTags(): Promise<Array<{ id: number; name: string; question_count: number }>> {
    return this.request('/forum/tags/');
  }

  async getPopularTags(): Promise<Array<{ id: number; name: string; question_count: number }>> {
    return this.request('/forum/tags/popular/');
  }

  async getForumStats(): Promise<{
    total_questions: number;
    total_answers: number;
    solved_questions: number;
    active_users: number;
    popular_tags: Array<{ name: string; question_count: number }>;
    recent_activity: Array<any>;
  }> {
    return this.request('/forum/stats/');
  }

  // Admin Stats
  async getUserStats(): Promise<{
    total_users: number;
    active_users: number;
    new_users_today: number;
    students_count: number;
    teachers_count: number;
    admins_count: number;
  }> {
    return this.request('/auth/stats/');
  }

  // Courses API
  async getCourses(): Promise<any[]> {
    return this.request('/courses/');
  }

  async getCourse(id: number): Promise<any> {
    return this.request(`/courses/${id}/`);
  }

  // Health check
  async healthCheck(): Promise<{ status: string }> {
    const response = await fetch(`${this.baseURL.replace('/api', '')}/health/`);
    if (response.ok) {
      return { status: 'OK' };
    }
    throw new Error('Health check failed');
  }
}

export const apiService = new ApiService();
export default apiService;
