from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import Question, Answer, Tag, Vote, Comment, QuestionView, UserReputation

User = get_user_model()


class AuthorSerializer(serializers.ModelSerializer):
    """
    Serializer for question/answer authors - matches frontend interface
    """
    name = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['id', 'name', 'avatar', 'role']
    
    def get_name(self, obj):
        return obj.get_full_name() or obj.username
    
    def get_avatar(self, obj):
        if obj.profile_picture:
            return obj.profile_picture.url
        return None


class TagSerializer(serializers.ModelSerializer):
    """
    Serializer for tags
    """
    class Meta:
        model = Tag
        fields = ['id', 'name', 'slug', 'description', 'color', 'question_count']


class CommentSerializer(serializers.ModelSerializer):
    """
    Serializer for comments
    """
    author = AuthorSerializer(read_only=True)
    
    class Meta:
        model = Comment
        fields = [
            'id', 'content', 'author', 'content_type', 'object_id',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class AnswerSerializer(serializers.ModelSerializer):
    """
    Serializer for answers
    """
    author = AuthorSerializer(read_only=True)
    comments = CommentSerializer(many=True, read_only=True)
    user_vote = serializers.SerializerMethodField()
    
    class Meta:
        model = Answer
        fields = [
            'id', 'content', 'author', 'votes', 'is_accepted',
            'created_at', 'updated_at', 'comments', 'user_vote'
        ]
        read_only_fields = ['id', 'author', 'votes', 'created_at', 'updated_at']
    
    def get_user_vote(self, obj):
        """Get current user's vote on this answer"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                vote = Vote.objects.get(
                    user=request.user,
                    content_type='answer',
                    object_id=obj.id
                )
                return vote.vote_type
            except Vote.DoesNotExist:
                return None
        return None


class QuestionListSerializer(serializers.ModelSerializer):
    """
    Serializer for question list - matches frontend ForumQuestion interface
    """
    author = AuthorSerializer(read_only=True)
    tags = serializers.StringRelatedField(many=True)
    answers = serializers.SerializerMethodField()
    solved = serializers.BooleanField(source='is_solved')
    
    class Meta:
        model = Question
        fields = [
            'id', 'title', 'content', 'author', 'tags', 'votes',
            'answers', 'views', 'created_at', 'solved'
        ]
    
    def get_answers(self, obj):
        return obj.answer_count


class QuestionDetailSerializer(serializers.ModelSerializer):
    """
    Detailed serializer for individual questions
    """
    author = AuthorSerializer(read_only=True)
    tags = TagSerializer(many=True, read_only=True)
    answers = AnswerSerializer(many=True, read_only=True)
    comments = CommentSerializer(many=True, read_only=True)
    user_vote = serializers.SerializerMethodField()
    answer_count = serializers.ReadOnlyField()
    
    class Meta:
        model = Question
        fields = [
            'id', 'title', 'content', 'slug', 'author', 'tags',
            'votes', 'views', 'is_solved', 'is_pinned', 'is_closed',
            'created_at', 'updated_at', 'last_activity',
            'answers', 'comments', 'user_vote', 'answer_count'
        ]
        read_only_fields = [
            'id', 'slug', 'author', 'votes', 'views', 'created_at',
            'updated_at', 'last_activity'
        ]
    
    def get_user_vote(self, obj):
        """Get current user's vote on this question"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            try:
                vote = Vote.objects.get(
                    user=request.user,
                    content_type='question',
                    object_id=obj.id
                )
                return vote.vote_type
            except Vote.DoesNotExist:
                return None
        return None


class QuestionCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating questions
    """
    tags = serializers.ListField(
        child=serializers.CharField(max_length=50),
        write_only=True
    )
    
    class Meta:
        model = Question
        fields = ['title', 'content', 'tags']
    
    def create(self, validated_data):
        tags_data = validated_data.pop('tags', [])
        question = Question.objects.create(
            author=self.context['request'].user,
            **validated_data
        )
        
        # Handle tags
        for tag_name in tags_data:
            tag, created = Tag.objects.get_or_create(
                name=tag_name.lower(),
                defaults={'name': tag_name.lower()}
            )
            question.tags.add(tag)
            
            # Update tag question count
            if created:
                tag.question_count = 1
            else:
                tag.question_count += 1
            tag.save()
        
        return question


class AnswerCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating answers
    """
    class Meta:
        model = Answer
        fields = ['content']
    
    def create(self, validated_data):
        question_id = self.context['question_id']
        question = Question.objects.get(id=question_id)
        
        answer = Answer.objects.create(
            question=question,
            author=self.context['request'].user,
            **validated_data
        )
        
        # Update question's last activity
        question.last_activity = answer.created_at
        question.save(update_fields=['last_activity'])
        
        return answer


class VoteSerializer(serializers.ModelSerializer):
    """
    Serializer for voting
    """
    class Meta:
        model = Vote
        fields = ['vote_type']
    
    def create(self, validated_data):
        user = self.context['request'].user
        content_type = self.context['content_type']
        object_id = self.context['object_id']
        
        # Check if user already voted
        existing_vote = Vote.objects.filter(
            user=user,
            content_type=content_type,
            object_id=object_id
        ).first()
        
        if existing_vote:
            if existing_vote.vote_type == validated_data['vote_type']:
                # Same vote - remove it
                existing_vote.delete()
                return None
            else:
                # Different vote - update it
                existing_vote.vote_type = validated_data['vote_type']
                existing_vote.save()
                return existing_vote
        else:
            # New vote
            return Vote.objects.create(
                user=user,
                content_type=content_type,
                object_id=object_id,
                **validated_data
            )


class UserReputationSerializer(serializers.ModelSerializer):
    """
    Serializer for user reputation
    """
    user = AuthorSerializer(read_only=True)
    
    class Meta:
        model = UserReputation
        fields = [
            'user', 'total_reputation', 'questions_asked', 'answers_given',
            'best_answers', 'helpful_votes_received', 'badges'
        ]


class ForumStatsSerializer(serializers.Serializer):
    """
    Serializer for forum statistics
    """
    total_questions = serializers.IntegerField()
    total_answers = serializers.IntegerField()
    solved_questions = serializers.IntegerField()
    active_users = serializers.IntegerField()
    popular_tags = serializers.ListField()
    recent_activity = serializers.ListField()


class SearchSerializer(serializers.Serializer):
    """
    Serializer for search parameters
    """
    q = serializers.CharField(required=False, allow_blank=True)
    tags = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True
    )
    author = serializers.CharField(required=False, allow_blank=True)
    solved = serializers.BooleanField(required=False)
    sort = serializers.ChoiceField(
        choices=['newest', 'oldest', 'votes', 'activity'],
        default='activity'
    )
