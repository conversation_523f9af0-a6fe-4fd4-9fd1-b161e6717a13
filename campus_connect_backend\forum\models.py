from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinLengthValidator
from django.utils.text import slugify
import uuid

User = get_user_model()


class Tag(models.Model):
    """
    Tags for categorizing questions
    """
    name = models.CharField(max_length=50, unique=True)
    slug = models.SlugField(max_length=50, unique=True)
    description = models.TextField(max_length=200, blank=True)
    color = models.CharField(max_length=7, default='#3B82F6')  # Hex color
    
    # Statistics
    question_count = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forum_tags'
        ordering = ['name']
        
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)


class Question(models.Model):
    """
    Forum questions - matches frontend ForumQuestion interface
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(
        max_length=200,
        validators=[MinLengthValidator(10)]
    )
    content = models.TextField(
        validators=[MinLengthValidator(20)]
    )
    slug = models.SlugField(max_length=250, unique=True, blank=True)
    
    # Author information
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='questions'
    )
    
    # Categorization
    tags = models.ManyToManyField(Tag, related_name='questions')
    
    # Engagement metrics
    votes = models.IntegerField(default=0)
    views = models.PositiveIntegerField(default=0)
    
    # Status
    is_solved = models.BooleanField(default=False)
    is_pinned = models.BooleanField(default=False)
    is_closed = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forum_questions'
        ordering = ['-last_activity']
        indexes = [
            models.Index(fields=['-created_at']),
            models.Index(fields=['-votes']),
            models.Index(fields=['-views']),
            models.Index(fields=['is_solved']),
        ]
        
    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.title)
            # Ensure uniqueness
            original_slug = self.slug
            counter = 1
            while Question.objects.filter(slug=self.slug).exists():
                self.slug = f"{original_slug}-{counter}"
                counter += 1
        super().save(*args, **kwargs)
    
    @property
    def answer_count(self):
        return self.answers.filter(is_deleted=False).count()
    
    @property
    def accepted_answer(self):
        return self.answers.filter(is_accepted=True, is_deleted=False).first()


class Answer(models.Model):
    """
    Answers to forum questions
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    question = models.ForeignKey(
        Question,
        on_delete=models.CASCADE,
        related_name='answers'
    )
    content = models.TextField(
        validators=[MinLengthValidator(10)]
    )
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='answers'
    )
    
    # Status
    is_accepted = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    
    # Engagement
    votes = models.IntegerField(default=0)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forum_answers'
        ordering = ['-is_accepted', '-votes', 'created_at']
        indexes = [
            models.Index(fields=['question', '-votes']),
            models.Index(fields=['author', '-created_at']),
        ]
        
    def __str__(self):
        return f"Answer to: {self.question.title}"
    
    def save(self, *args, **kwargs):
        # If this answer is being accepted, unaccept others
        if self.is_accepted:
            Answer.objects.filter(
                question=self.question,
                is_accepted=True
            ).exclude(id=self.id).update(is_accepted=False)
            
            # Mark question as solved
            self.question.is_solved = True
            self.question.save(update_fields=['is_solved'])
        
        super().save(*args, **kwargs)


class Vote(models.Model):
    """
    Votes on questions and answers
    """
    VOTE_CHOICES = [
        (1, 'Upvote'),
        (-1, 'Downvote'),
    ]
    
    CONTENT_TYPE_CHOICES = [
        ('question', 'Question'),
        ('answer', 'Answer'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    content_type = models.CharField(max_length=10, choices=CONTENT_TYPE_CHOICES)
    object_id = models.UUIDField()
    vote_type = models.IntegerField(choices=VOTE_CHOICES)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forum_votes'
        unique_together = ['user', 'content_type', 'object_id']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
        ]
        
    def __str__(self):
        return f"{self.user.username} {self.get_vote_type_display()} on {self.content_type}"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.update_vote_count()
    
    def delete(self, *args, **kwargs):
        super().delete(*args, **kwargs)
        self.update_vote_count()
    
    def update_vote_count(self):
        """Update vote count on the related object"""
        if self.content_type == 'question':
            question = Question.objects.get(id=self.object_id)
            question.votes = Vote.objects.filter(
                content_type='question',
                object_id=self.object_id
            ).aggregate(
                total=models.Sum('vote_type')
            )['total'] or 0
            question.save(update_fields=['votes'])
            
        elif self.content_type == 'answer':
            answer = Answer.objects.get(id=self.object_id)
            answer.votes = Vote.objects.filter(
                content_type='answer',
                object_id=self.object_id
            ).aggregate(
                total=models.Sum('vote_type')
            )['total'] or 0
            answer.save(update_fields=['votes'])


class Comment(models.Model):
    """
    Comments on questions and answers
    """
    CONTENT_TYPE_CHOICES = [
        ('question', 'Question'),
        ('answer', 'Answer'),
    ]
    
    content = models.TextField(max_length=500)
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    content_type = models.CharField(max_length=10, choices=CONTENT_TYPE_CHOICES)
    object_id = models.UUIDField()
    
    is_deleted = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forum_comments'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['content_type', 'object_id', 'created_at']),
        ]
        
    def __str__(self):
        return f"Comment by {self.author.username}"


class QuestionView(models.Model):
    """
    Track question views for analytics
    """
    question = models.ForeignKey(Question, on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'forum_question_views'
        unique_together = ['question', 'user', 'ip_address']
        
    def __str__(self):
        return f"View of {self.question.title}"


class UserReputation(models.Model):
    """
    Track user reputation in the forum
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='forum_reputation')
    
    # Reputation points
    total_reputation = models.IntegerField(default=0)
    
    # Activity counts
    questions_asked = models.PositiveIntegerField(default=0)
    answers_given = models.PositiveIntegerField(default=0)
    best_answers = models.PositiveIntegerField(default=0)
    helpful_votes_received = models.PositiveIntegerField(default=0)
    
    # Badges/Achievements
    badges = models.JSONField(default=list, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'forum_user_reputation'
        
    def __str__(self):
        return f"{self.user.username} - {self.total_reputation} reputation"
    
    def calculate_reputation(self):
        """Calculate total reputation based on activity"""
        reputation = 0
        
        # Points for questions
        reputation += self.questions_asked * 5
        
        # Points for answers
        reputation += self.answers_given * 10
        
        # Points for best answers
        reputation += self.best_answers * 25
        
        # Points for helpful votes
        reputation += self.helpful_votes_received * 2
        
        self.total_reputation = reputation
        self.save(update_fields=['total_reputation'])
        
        return reputation
