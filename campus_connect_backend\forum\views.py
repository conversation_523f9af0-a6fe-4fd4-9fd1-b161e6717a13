from rest_framework import generics, status, permissions, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.db.models import Q, Count, F
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from datetime import <PERSON><PERSON><PERSON>

from .models import Question, Answer, Tag, Vote, Comment, QuestionView, UserReputation
from .serializers import (
    QuestionListSerializer, QuestionDetailSerializer, QuestionCreateSerializer,
    AnswerSerializer, AnswerCreateSerializer, TagSerializer, VoteSerializer,
    CommentSerializer, UserReputationSerializer, ForumStatsSerializer,
    SearchSerializer
)


class QuestionListCreateView(generics.ListCreateAPIView):
    """
    List questions or create new question
    GET/POST /api/forum/questions/
    """
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['title', 'content']
    ordering_fields = ['created_at', 'votes', 'views', 'last_activity']
    ordering = ['-last_activity']
    
    def get_queryset(self):
        queryset = Question.objects.select_related('author').prefetch_related('tags')
        
        # Filter by solved status
        solved = self.request.query_params.get('solved')
        if solved is not None:
            queryset = queryset.filter(is_solved=solved.lower() == 'true')
        
        # Filter by tags
        tags = self.request.query_params.getlist('tags')
        if tags:
            queryset = queryset.filter(tags__name__in=tags).distinct()
        
        # Filter by author
        author = self.request.query_params.get('author')
        if author:
            queryset = queryset.filter(author__username=author)
        
        # Filter user's own questions
        my_questions = self.request.query_params.get('my_questions')
        if my_questions == 'true':
            queryset = queryset.filter(author=self.request.user)
        
        # Filter unanswered questions
        unanswered = self.request.query_params.get('unanswered')
        if unanswered == 'true':
            queryset = queryset.annotate(
                answer_count=Count('answers', filter=Q(answers__is_deleted=False))
            ).filter(answer_count=0)
        
        return queryset
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return QuestionCreateSerializer
        return QuestionListSerializer
    
    def perform_create(self, serializer):
        serializer.save()


class QuestionDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete a question
    GET/PUT/DELETE /api/forum/questions/{id}/
    """
    queryset = Question.objects.select_related('author').prefetch_related(
        'tags', 'answers__author', 'comments__author'
    )
    serializer_class = QuestionDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'
    
    def retrieve(self, request, *args, **kwargs):
        question = self.get_object()
        
        # Track question view
        self.track_question_view(question, request)
        
        # Increment view count
        Question.objects.filter(id=question.id).update(views=F('views') + 1)
        
        serializer = self.get_serializer(question)
        return Response(serializer.data)
    
    def track_question_view(self, question, request):
        """Track question view for analytics"""
        ip_address = self.get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        QuestionView.objects.get_or_create(
            question=question,
            user=request.user if request.user.is_authenticated else None,
            ip_address=ip_address,
            defaults={'user_agent': user_agent}
        )
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class AnswerListCreateView(generics.ListCreateAPIView):
    """
    List answers for a question or create new answer
    GET/POST /api/forum/questions/{question_id}/answers/
    """
    serializer_class = AnswerSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        question_id = self.kwargs['question_id']
        return Answer.objects.filter(
            question_id=question_id,
            is_deleted=False
        ).select_related('author').prefetch_related('comments__author')
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return AnswerCreateSerializer
        return AnswerSerializer
    
    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['question_id'] = self.kwargs['question_id']
        return context


class AnswerDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    Retrieve, update or delete an answer
    GET/PUT/DELETE /api/forum/answers/{id}/
    """
    queryset = Answer.objects.select_related('author', 'question')
    serializer_class = AnswerSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def accept_answer(request, answer_id):
    """
    Accept an answer as the best answer
    POST /api/forum/answers/{answer_id}/accept/
    """
    try:
        answer = Answer.objects.get(id=answer_id)
        
        # Only question author can accept answers
        if request.user != answer.question.author:
            return Response(
                {'error': 'Only the question author can accept answers'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Toggle acceptance
        answer.is_accepted = not answer.is_accepted
        answer.save()
        
        return Response({
            'message': 'Answer accepted' if answer.is_accepted else 'Answer unaccepted',
            'is_accepted': answer.is_accepted
        })
        
    except Answer.DoesNotExist:
        return Response(
            {'error': 'Answer not found'},
            status=status.HTTP_404_NOT_FOUND
        )


class VoteView(APIView):
    """
    Vote on questions or answers
    POST /api/forum/vote/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        content_type = request.data.get('content_type')  # 'question' or 'answer'
        object_id = request.data.get('object_id')
        vote_type = request.data.get('vote_type')  # 1 or -1
        
        if not all([content_type, object_id, vote_type]):
            return Response(
                {'error': 'Missing required fields'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate content type
        if content_type not in ['question', 'answer']:
            return Response(
                {'error': 'Invalid content type'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Validate vote type
        if int(vote_type) not in [1, -1]:
            return Response(
                {'error': 'Invalid vote type'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if object exists
        if content_type == 'question':
            try:
                obj = Question.objects.get(id=object_id)
            except Question.DoesNotExist:
                return Response(
                    {'error': 'Question not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            try:
                obj = Answer.objects.get(id=object_id)
            except Answer.DoesNotExist:
                return Response(
                    {'error': 'Answer not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        
        # Prevent self-voting
        if obj.author == request.user:
            return Response(
                {'error': 'Cannot vote on your own content'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        serializer = VoteSerializer(
            data={'vote_type': vote_type},
            context={
                'request': request,
                'content_type': content_type,
                'object_id': object_id
            }
        )
        
        if serializer.is_valid():
            vote = serializer.save()
            
            # Get updated vote count
            if content_type == 'question':
                obj.refresh_from_db()
                vote_count = obj.votes
            else:
                obj.refresh_from_db()
                vote_count = obj.votes
            
            return Response({
                'message': 'Vote recorded',
                'vote_count': vote_count,
                'user_vote': vote.vote_type if vote else None
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TagListView(generics.ListAPIView):
    """
    List all tags
    GET /api/forum/tags/
    """
    queryset = Tag.objects.all()
    serializer_class = TagSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'question_count']
    ordering = ['-question_count']


class PopularTagsView(APIView):
    """
    Get popular tags
    GET /api/forum/tags/popular/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        popular_tags = Tag.objects.filter(
            question_count__gt=0
        ).order_by('-question_count')[:20]
        
        serializer = TagSerializer(popular_tags, many=True)
        return Response(serializer.data)


class ForumStatsView(APIView):
    """
    Get forum statistics
    GET /api/forum/stats/
    """
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        # Basic stats
        total_questions = Question.objects.count()
        total_answers = Answer.objects.filter(is_deleted=False).count()
        solved_questions = Question.objects.filter(is_solved=True).count()
        
        # Active users (posted in last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        active_users = Question.objects.filter(
            created_at__gte=thirty_days_ago
        ).values('author').distinct().count()
        
        # Popular tags
        popular_tags = list(Tag.objects.filter(
            question_count__gt=0
        ).order_by('-question_count')[:10].values('name', 'question_count'))
        
        # Recent activity
        recent_questions = Question.objects.select_related('author').order_by(
            '-created_at'
        )[:5]
        
        recent_activity = []
        for q in recent_questions:
            recent_activity.append({
                'type': 'question',
                'title': q.title,
                'author': q.author.get_full_name(),
                'created_at': q.created_at
            })
        
        stats = {
            'total_questions': total_questions,
            'total_answers': total_answers,
            'solved_questions': solved_questions,
            'active_users': active_users,
            'popular_tags': popular_tags,
            'recent_activity': recent_activity
        }
        
        serializer = ForumStatsSerializer(stats)
        return Response(serializer.data)


class UserReputationView(generics.RetrieveAPIView):
    """
    Get user reputation
    GET /api/forum/reputation/{user_id}/
    """
    serializer_class = UserReputationSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_object(self):
        user_id = self.kwargs.get('user_id')
        reputation, created = UserReputation.objects.get_or_create(
            user_id=user_id
        )
        
        if created:
            reputation.calculate_reputation()
        
        return reputation
