import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { CheckCircle, AlertCircle, Info, X, Zap, Trophy, Star } from 'lucide-react';

interface AnimatedToastProps {
  type: 'success' | 'error' | 'info' | 'achievement' | 'celebration';
  title: string;
  message?: string;
  duration?: number;
  onClose: () => void;
}

const AnimatedToast = ({ type, title, message, duration = 4000, onClose }: AnimatedToastProps) => {
  const toastRef = useRef<HTMLDivElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const toast = toastRef.current;
    const progress = progressRef.current;
    
    if (!toast || !progress) return;

    // Entrance animation
    const tl = gsap.timeline();
    
    // Initial state
    gsap.set(toast, { x: 300, opacity: 0, scale: 0.8 });
    gsap.set(progress, { width: "100%" });

    // Animate in
    tl.to(toast, {
      x: 0,
      opacity: 1,
      scale: 1,
      duration: 0.5,
      ease: "back.out(1.7)"
    });

    // Add special effects based on type
    if (type === 'achievement' || type === 'celebration') {
      // Create confetti particles
      createConfetti(toast);
      
      // Add glow effect
      tl.to(toast, {
        boxShadow: "0 0 30px rgba(251, 191, 36, 0.6)",
        duration: 0.5,
        yoyo: true,
        repeat: 3,
        ease: "power2.inOut"
      }, "-=0.3");
    }

    // Progress bar animation
    tl.to(progress, {
      width: "0%",
      duration: duration / 1000,
      ease: "none"
    }, "-=0.2");

    // Auto close
    const timer = setTimeout(() => {
      exitAnimation();
    }, duration);

    const exitAnimation = () => {
      gsap.to(toast, {
        x: 300,
        opacity: 0,
        scale: 0.8,
        duration: 0.3,
        ease: "power2.in",
        onComplete: onClose
      });
    };

    // Cleanup
    return () => {
      clearTimeout(timer);
      tl.kill();
    };
  }, [type, duration, onClose]);

  const createConfetti = (container: HTMLElement) => {
    const colors = ['#FFD700', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
    
    for (let i = 0; i < 15; i++) {
      const confetti = document.createElement('div');
      confetti.style.cssText = `
        position: absolute;
        width: 6px;
        height: 6px;
        background: ${colors[Math.floor(Math.random() * colors.length)]};
        border-radius: 50%;
        pointer-events: none;
        top: 50%;
        left: 50%;
        z-index: 1000;
      `;
      
      container.appendChild(confetti);
      
      // Animate confetti
      gsap.to(confetti, {
        x: (Math.random() - 0.5) * 200,
        y: (Math.random() - 0.5) * 200,
        rotation: Math.random() * 360,
        opacity: 0,
        scale: 0,
        duration: 1.5,
        delay: Math.random() * 0.5,
        ease: "power2.out",
        onComplete: () => confetti.remove()
      });
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      case 'info':
        return <Info className="h-5 w-5 text-blue-600" />;
      case 'achievement':
        return <Trophy className="h-5 w-5 text-yellow-600" />;
      case 'celebration':
        return <Star className="h-5 w-5 text-purple-600" />;
      default:
        return <Info className="h-5 w-5 text-blue-600" />;
    }
  };

  const getStyles = () => {
    const baseStyles = "relative bg-white border rounded-lg shadow-lg p-4 max-w-sm overflow-hidden";
    
    switch (type) {
      case 'success':
        return `${baseStyles} border-green-200`;
      case 'error':
        return `${baseStyles} border-red-200`;
      case 'info':
        return `${baseStyles} border-blue-200`;
      case 'achievement':
        return `${baseStyles} border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50`;
      case 'celebration':
        return `${baseStyles} border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50`;
      default:
        return `${baseStyles} border-gray-200`;
    }
  };

  const getProgressColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'info':
        return 'bg-blue-500';
      case 'achievement':
        return 'bg-yellow-500';
      case 'celebration':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div ref={toastRef} className={getStyles()}>
      {/* Progress bar */}
      <div className="absolute top-0 left-0 h-1 bg-gray-200 w-full">
        <div 
          ref={progressRef}
          className={`h-full ${getProgressColor()} transition-all duration-100`}
        />
      </div>

      {/* Content */}
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div>
              <h4 className="font-medium text-gray-900">
                {title}
                {(type === 'achievement' || type === 'celebration') && (
                  <span className="ml-2">🎉</span>
                )}
              </h4>
              {message && (
                <p className="text-sm text-gray-600 mt-1">{message}</p>
              )}
            </div>
            
            <button
              onClick={onClose}
              className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Special effects overlay */}
      {(type === 'achievement' || type === 'celebration') && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-2 right-2 animate-bounce">
            <Zap className="h-4 w-4 text-yellow-500" />
          </div>
        </div>
      )}
    </div>
  );
};

// Toast container component
export const AnimatedToastContainer = ({ toasts }: { toasts: any[] }) => {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast) => (
        <AnimatedToast key={toast.id} {...toast} />
      ))}
    </div>
  );
};

export default AnimatedToast;
