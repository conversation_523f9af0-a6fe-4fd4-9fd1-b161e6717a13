import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface LoadingSpinnerProps {
  variant?: 'dots' | 'pulse' | 'bounce' | 'wave' | 'academic';
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}

const LoadingSpinner = ({ 
  variant = 'dots', 
  size = 'md', 
  color = '#3B82F6',
  className = '' 
}: LoadingSpinnerProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let animation: gsap.core.Timeline;

    switch (variant) {
      case 'dots':
        animation = createDotsAnimation(container, color);
        break;
      case 'pulse':
        animation = createPulseAnimation(container, color);
        break;
      case 'bounce':
        animation = createBounceAnimation(container, color);
        break;
      case 'wave':
        animation = createWaveAnimation(container, color);
        break;
      case 'academic':
        animation = createAcademicAnimation(container, color);
        break;
    }

    return () => {
      if (animation) animation.kill();
    };
  }, [variant, color]);

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'w-6 h-6';
      case 'lg': return 'w-16 h-16';
      default: return 'w-10 h-10';
    }
  };

  return (
    <div 
      ref={containerRef}
      className={`${getSizeClasses()} ${className} flex items-center justify-center`}
    />
  );
};

const createDotsAnimation = (container: HTMLElement, color: string) => {
  const dots = [];
  
  for (let i = 0; i < 3; i++) {
    const dot = document.createElement('div');
    dot.style.cssText = `
      width: 8px;
      height: 8px;
      background: ${color};
      border-radius: 50%;
      margin: 0 2px;
    `;
    container.appendChild(dot);
    dots.push(dot);
  }

  const tl = gsap.timeline({ repeat: -1 });
  
  dots.forEach((dot, index) => {
    tl.to(dot, {
      scale: 1.5,
      opacity: 0.7,
      duration: 0.4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: 1
    }, index * 0.2);
  });

  return tl;
};

const createPulseAnimation = (container: HTMLElement, color: string) => {
  const circle = document.createElement('div');
  circle.style.cssText = `
    width: 100%;
    height: 100%;
    background: ${color};
    border-radius: 50%;
    opacity: 0.6;
  `;
  container.appendChild(circle);

  const tl = gsap.timeline({ repeat: -1 });
  
  tl.to(circle, {
    scale: 1.2,
    opacity: 0.3,
    duration: 1,
    ease: "power2.inOut",
    yoyo: true,
    repeat: 1
  });

  return tl;
};

const createBounceAnimation = (container: HTMLElement, color: string) => {
  const balls = [];
  
  for (let i = 0; i < 3; i++) {
    const ball = document.createElement('div');
    ball.style.cssText = `
      width: 10px;
      height: 10px;
      background: ${color};
      border-radius: 50%;
      margin: 0 2px;
    `;
    container.appendChild(ball);
    balls.push(ball);
  }

  const tl = gsap.timeline({ repeat: -1 });
  
  balls.forEach((ball, index) => {
    tl.to(ball, {
      y: -20,
      duration: 0.5,
      ease: "power2.out",
      yoyo: true,
      repeat: 1
    }, index * 0.1);
  });

  return tl;
};

const createWaveAnimation = (container: HTMLElement, color: string) => {
  const bars = [];
  
  for (let i = 0; i < 5; i++) {
    const bar = document.createElement('div');
    bar.style.cssText = `
      width: 3px;
      height: 20px;
      background: ${color};
      margin: 0 1px;
      border-radius: 2px;
    `;
    container.appendChild(bar);
    bars.push(bar);
  }

  const tl = gsap.timeline({ repeat: -1 });
  
  bars.forEach((bar, index) => {
    tl.to(bar, {
      scaleY: 0.3,
      duration: 0.4,
      ease: "power2.inOut",
      yoyo: true,
      repeat: 1
    }, index * 0.1);
  });

  return tl;
};

const createAcademicAnimation = (container: HTMLElement, color: string) => {
  // Create a book-like animation
  const book = document.createElement('div');
  book.style.cssText = `
    width: 24px;
    height: 18px;
    background: ${color};
    border-radius: 2px;
    position: relative;
  `;
  
  const page = document.createElement('div');
  page.style.cssText = `
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 1px;
  `;
  
  book.appendChild(page);
  container.appendChild(book);

  const tl = gsap.timeline({ repeat: -1 });
  
  tl.to(book, {
    rotationY: 180,
    duration: 1,
    ease: "power2.inOut"
  })
  .to(book, {
    rotationY: 0,
    duration: 1,
    ease: "power2.inOut"
  });

  return tl;
};

// Skeleton loader component
export const SkeletonLoader = ({ 
  lines = 3, 
  className = '',
  animated = true 
}: { 
  lines?: number; 
  className?: string;
  animated?: boolean;
}) => {
  const skeletonRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!animated || !skeletonRef.current) return;

    const elements = skeletonRef.current.querySelectorAll('.skeleton-line');
    
    gsap.to(elements, {
      opacity: 0.5,
      duration: 1,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      stagger: 0.2
    });
  }, [animated]);

  return (
    <div ref={skeletonRef} className={`space-y-3 ${className}`}>
      {Array.from({ length: lines }).map((_, index) => (
        <div
          key={index}
          className={`skeleton-line h-4 bg-gray-200 rounded animate-pulse ${
            index === lines - 1 ? 'w-3/4' : 'w-full'
          }`}
        />
      ))}
    </div>
  );
};

// Progress loader with fun animations
export const ProgressLoader = ({ 
  progress, 
  label = 'Loading...',
  showPercentage = true,
  animated = true 
}: { 
  progress: number; 
  label?: string;
  showPercentage?: boolean;
  animated?: boolean;
}) => {
  const progressRef = useRef<HTMLDivElement>(null);
  const labelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!animated) return;

    const progressBar = progressRef.current;
    const labelElement = labelRef.current;

    if (progressBar) {
      gsap.to(progressBar, {
        width: `${progress}%`,
        duration: 0.5,
        ease: "power2.out"
      });
    }

    if (labelElement && progress === 100) {
      gsap.to(labelElement, {
        scale: 1.1,
        color: '#10B981',
        duration: 0.3,
        yoyo: true,
        repeat: 1
      });
    }
  }, [progress, animated]);

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="flex justify-between items-center mb-2">
        <span ref={labelRef} className="text-sm font-medium text-gray-700">
          {label}
        </span>
        {showPercentage && (
          <span className="text-sm text-gray-500">{progress}%</span>
        )}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
        <div
          ref={progressRef}
          className="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transition-all duration-300 relative"
          style={{ width: '0%' }}
        >
          {animated && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer" />
          )}
        </div>
      </div>
    </div>
  );
};

// Fun loading messages
export const LoadingMessages = ({ 
  messages = [
    "Loading awesome content...",
    "Preparing your dashboard...",
    "Almost there...",
    "Just a moment more..."
  ],
  interval = 2000 
}: { 
  messages?: string[]; 
  interval?: number;
}) => {
  const messageRef = useRef<HTMLDivElement>(null);
  const currentIndex = useRef(0);

  useEffect(() => {
    const element = messageRef.current;
    if (!element) return;

    const updateMessage = () => {
      gsap.to(element, {
        opacity: 0,
        y: -10,
        duration: 0.3,
        onComplete: () => {
          currentIndex.current = (currentIndex.current + 1) % messages.length;
          element.textContent = messages[currentIndex.current];
          gsap.to(element, {
            opacity: 1,
            y: 0,
            duration: 0.3
          });
        }
      });
    };

    const timer = setInterval(updateMessage, interval);
    
    return () => clearInterval(timer);
  }, [messages, interval]);

  return (
    <div 
      ref={messageRef}
      className="text-center text-gray-600 font-medium"
    >
      {messages[0]}
    </div>
  );
};

export default LoadingSpinner;
