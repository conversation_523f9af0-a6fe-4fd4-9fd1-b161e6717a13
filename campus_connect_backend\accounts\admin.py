from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import User, UserProfile, UserSession, PasswordResetToken


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """
    Custom User admin interface
    """
    list_display = [
        'email', 'username', 'first_name', 'last_name', 'role',
        'is_active', 'email_verified', 'created_at'
    ]
    list_filter = [
        'role', 'is_active', 'email_verified', 'is_2fa_enabled',
        'created_at', 'last_login'
    ]
    search_fields = ['email', 'username', 'first_name', 'last_name', 'student_id']
    ordering = ['-created_at']
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('Campus Connect Info', {
            'fields': (
                'role', 'phone', 'profile_picture', 'bio',
                'student_id', 'department', 'year_of_study', 'gpa'
            )
        }),
        ('Security', {
            'fields': ('is_2fa_enabled', 'email_verified', 'last_login_ip')
        }),
        ('Preferences', {
            'fields': ('theme_preference', 'notification_preferences')
        }),
    )
    
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('Campus Connect Info', {
            'fields': ('email', 'role', 'first_name', 'last_name', 'phone')
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('profile')


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    User Profile admin interface
    """
    list_display = [
        'user', 'major', 'total_points', 'study_streak',
        'questions_asked', 'questions_answered', 'public_profile'
    ]
    list_filter = [
        'public_profile', 'receive_email_notifications',
        'receive_sms_notifications', 'created_at'
    ]
    search_fields = ['user__email', 'user__first_name', 'user__last_name', 'major']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Academic Info', {
            'fields': (
                'major', 'minor', 'enrollment_date', 'graduation_date'
            )
        }),
        ('Social Links', {
            'fields': ('linkedin_url', 'github_url', 'portfolio_url')
        }),
        ('Preferences', {
            'fields': (
                'receive_email_notifications', 'receive_sms_notifications',
                'public_profile'
            )
        }),
        ('Gamification', {
            'fields': (
                'total_points', 'study_streak', 'questions_asked',
                'questions_answered', 'best_answer_count'
            )
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(UserSession)
class UserSessionAdmin(admin.ModelAdmin):
    """
    User Session admin interface
    """
    list_display = [
        'user', 'ip_address', 'device_type', 'location',
        'is_active', 'created_at', 'last_activity'
    ]
    list_filter = ['is_active', 'device_type', 'created_at']
    search_fields = ['user__email', 'ip_address', 'location']
    readonly_fields = ['created_at', 'last_activity']
    ordering = ['-last_activity']
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


@admin.register(PasswordResetToken)
class PasswordResetTokenAdmin(admin.ModelAdmin):
    """
    Password Reset Token admin interface
    """
    list_display = [
        'user', 'token_preview', 'created_at', 'expires_at',
        'is_expired', 'is_used'
    ]
    list_filter = ['created_at', 'expires_at', 'used_at']
    search_fields = ['user__email', 'token']
    readonly_fields = ['created_at', 'used_at', 'is_expired', 'is_used']
    ordering = ['-created_at']
    
    def token_preview(self, obj):
        """Show first 10 characters of token"""
        return f"{obj.token[:10]}..."
    token_preview.short_description = "Token Preview"
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user')


# Customize admin site
admin.site.site_header = "Campus Connect Administration"
admin.site.site_title = "Campus Connect Admin"
admin.site.index_title = "Welcome to Campus Connect Administration"
