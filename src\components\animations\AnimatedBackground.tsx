import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface AnimatedBackgroundProps {
  variant?: 'particles' | 'waves' | 'geometric' | 'minimal';
  className?: string;
}

const AnimatedBackground = ({ variant = 'particles', className = '' }: AnimatedBackgroundProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let animation: gsap.core.Timeline;

    switch (variant) {
      case 'particles':
        animation = createParticleAnimation(container);
        break;
      case 'waves':
        animation = createWaveAnimation(container);
        break;
      case 'geometric':
        animation = createGeometricAnimation(container);
        break;
      case 'minimal':
        animation = createMinimalAnimation(container);
        break;
    }

    return () => {
      if (animation) {
        animation.kill();
      }
      // Clean up any created elements
      container.innerHTML = '';
    };
  }, [variant]);

  return (
    <div 
      ref={containerRef}
      className={`fixed inset-0 pointer-events-none overflow-hidden ${className}`}
      style={{ zIndex: -1 }}
    />
  );
};

const createParticleAnimation = (container: HTMLElement) => {
  const particleCount = 50;
  const particles: HTMLElement[] = [];

  // Create particles
  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    particle.style.cssText = `
      position: absolute;
      width: ${Math.random() * 4 + 2}px;
      height: ${Math.random() * 4 + 2}px;
      background: hsl(${Math.random() * 60 + 200}, 70%, 70%);
      border-radius: 50%;
      opacity: ${Math.random() * 0.5 + 0.3};
    `;
    
    // Random starting position
    gsap.set(particle, {
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
    });

    container.appendChild(particle);
    particles.push(particle);
  }

  // Animate particles
  const tl = gsap.timeline({ repeat: -1 });
  
  particles.forEach((particle, index) => {
    tl.to(particle, {
      x: `+=${Math.random() * 200 - 100}`,
      y: `+=${Math.random() * 200 - 100}`,
      rotation: 360,
      duration: Math.random() * 10 + 10,
      ease: "none",
      repeat: -1,
      yoyo: true,
      delay: index * 0.1
    }, 0);
  });

  return tl;
};

const createWaveAnimation = (container: HTMLElement) => {
  const waveCount = 3;
  const waves: HTMLElement[] = [];

  for (let i = 0; i < waveCount; i++) {
    const wave = document.createElement('div');
    wave.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, 
        hsla(${200 + i * 20}, 70%, 70%, 0.1) 0%, 
        hsla(${220 + i * 20}, 70%, 70%, 0.05) 100%);
      border-radius: 50%;
      transform-origin: center;
    `;
    
    container.appendChild(wave);
    waves.push(wave);
  }

  const tl = gsap.timeline({ repeat: -1 });
  
  waves.forEach((wave, index) => {
    tl.to(wave, {
      scale: 1.5,
      rotation: 360,
      opacity: 0,
      duration: 8 + index * 2,
      ease: "power1.inOut",
      repeat: -1,
      delay: index * 2
    }, 0);
  });

  return tl;
};

const createGeometricAnimation = (container: HTMLElement) => {
  const shapeCount = 20;
  const shapes: HTMLElement[] = [];

  const shapeTypes = ['circle', 'square', 'triangle'];

  for (let i = 0; i < shapeCount; i++) {
    const shape = document.createElement('div');
    const shapeType = shapeTypes[Math.floor(Math.random() * shapeTypes.length)];
    const size = Math.random() * 30 + 10;
    
    let shapeStyles = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      opacity: ${Math.random() * 0.3 + 0.1};
    `;

    switch (shapeType) {
      case 'circle':
        shapeStyles += `
          background: hsl(${Math.random() * 360}, 70%, 60%);
          border-radius: 50%;
        `;
        break;
      case 'square':
        shapeStyles += `
          background: hsl(${Math.random() * 360}, 70%, 60%);
          transform: rotate(45deg);
        `;
        break;
      case 'triangle':
        shapeStyles += `
          width: 0;
          height: 0;
          border-left: ${size/2}px solid transparent;
          border-right: ${size/2}px solid transparent;
          border-bottom: ${size}px solid hsl(${Math.random() * 360}, 70%, 60%);
        `;
        break;
    }

    shape.style.cssText = shapeStyles;
    
    gsap.set(shape, {
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
    });

    container.appendChild(shape);
    shapes.push(shape);
  }

  const tl = gsap.timeline({ repeat: -1 });
  
  shapes.forEach((shape, index) => {
    tl.to(shape, {
      x: `+=${Math.random() * 300 - 150}`,
      y: `+=${Math.random() * 300 - 150}`,
      rotation: Math.random() * 360,
      scale: Math.random() * 0.5 + 0.5,
      duration: Math.random() * 15 + 10,
      ease: "power1.inOut",
      repeat: -1,
      yoyo: true,
      delay: index * 0.2
    }, 0);
  });

  return tl;
};

const createMinimalAnimation = (container: HTMLElement) => {
  const dotCount = 15;
  const dots: HTMLElement[] = [];

  for (let i = 0; i < dotCount; i++) {
    const dot = document.createElement('div');
    dot.style.cssText = `
      position: absolute;
      width: 2px;
      height: 2px;
      background: hsl(220, 30%, 60%);
      border-radius: 50%;
      opacity: 0.4;
    `;
    
    gsap.set(dot, {
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
    });

    container.appendChild(dot);
    dots.push(dot);
  }

  const tl = gsap.timeline({ repeat: -1 });
  
  dots.forEach((dot, index) => {
    tl.to(dot, {
      x: `+=${Math.random() * 100 - 50}`,
      y: `+=${Math.random() * 100 - 50}`,
      opacity: Math.random() * 0.5 + 0.2,
      duration: Math.random() * 20 + 15,
      ease: "power1.inOut",
      repeat: -1,
      yoyo: true,
      delay: index * 0.5
    }, 0);
  });

  return tl;
};

export default AnimatedBackground;
