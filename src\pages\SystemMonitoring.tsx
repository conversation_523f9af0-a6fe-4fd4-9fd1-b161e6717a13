import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import AnimatedBackground from "@/components/animations/AnimatedBackground";
import AnimatedCard, { AnimatedStatsCard } from "@/components/animations/AnimatedCard";
import AnimatedProgress, { AnimatedCircularProgress } from "@/components/animations/AnimatedProgress";
import { usePageEntrance } from "@/hooks/useGSAPAnimations";
import { 
  Activity, 
  Server, 
  Database, 
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Shield,
  Globe,
  Users,
  RefreshCw,
  Settings,
  Bell,
  TrendingUp,
  TrendingDown
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

const SystemMonitoring = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // Add page entrance animations
  usePageEntrance([]);

  // Mock real-time system data
  const [systemMetrics, setSystemMetrics] = useState({
    server: {
      status: "healthy",
      uptime: "15d 7h 23m",
      cpu: 45,
      memory: 67,
      storage: 78,
      network: 23,
      temperature: 42,
      load: 1.2
    },
    database: {
      status: "healthy",
      connections: 156,
      maxConnections: 200,
      queryTime: 12.5,
      cacheHitRate: 94.2,
      size: "2.3 GB",
      backupStatus: "completed"
    },
    application: {
      status: "healthy",
      activeUsers: 892,
      requestsPerMinute: 1247,
      responseTime: 145,
      errorRate: 0.12,
      version: "2.1.4",
      lastDeploy: "2024-05-30 14:30"
    },
    security: {
      status: "secure",
      failedLogins: 3,
      blockedIPs: 12,
      lastScan: "2024-05-31 02:00",
      vulnerabilities: 0,
      sslExpiry: "2024-12-15"
    }
  });

  const [alerts, setAlerts] = useState([
    {
      id: "1",
      type: "warning",
      title: "Utilisation mémoire élevée",
      message: "L'utilisation de la mémoire a atteint 67%. Surveillance recommandée.",
      timestamp: "Il y a 5 minutes",
      severity: "medium"
    },
    {
      id: "2",
      type: "info",
      title: "Sauvegarde complétée",
      message: "Sauvegarde automatique de la base de données terminée avec succès.",
      timestamp: "Il y a 15 minutes",
      severity: "low"
    },
    {
      id: "3",
      type: "success",
      title: "Mise à jour de sécurité",
      message: "Patch de sécurité appliqué avec succès. Système redémarré.",
      timestamp: "Il y a 2 heures",
      severity: "low"
    }
  ]);

  const [performanceHistory, setPerformanceHistory] = useState([
    { time: "00:00", cpu: 35, memory: 62, users: 234 },
    { time: "04:00", cpu: 28, memory: 58, users: 156 },
    { time: "08:00", cpu: 52, memory: 71, users: 678 },
    { time: "12:00", cpu: 67, memory: 78, users: 892 },
    { time: "16:00", cpu: 45, memory: 67, users: 756 },
    { time: "20:00", cpu: 38, memory: 64, users: 445 }
  ]);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      setSystemMetrics(prev => ({
        ...prev,
        server: {
          ...prev.server,
          cpu: Math.max(20, Math.min(80, prev.server.cpu + (Math.random() - 0.5) * 10)),
          memory: Math.max(40, Math.min(90, prev.server.memory + (Math.random() - 0.5) * 5)),
          network: Math.max(10, Math.min(50, prev.server.network + (Math.random() - 0.5) * 15))
        },
        application: {
          ...prev.application,
          activeUsers: Math.max(500, Math.min(1200, prev.application.activeUsers + Math.floor((Math.random() - 0.5) * 50))),
          requestsPerMinute: Math.max(800, Math.min(2000, prev.application.requestsPerMinute + Math.floor((Math.random() - 0.5) * 200))),
          responseTime: Math.max(80, Math.min(300, prev.application.responseTime + (Math.random() - 0.5) * 40))
        }
      }));
      setLastUpdate(new Date());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
      case "secure":
        return "text-green-600 bg-green-50 border-green-200";
      case "warning":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "critical":
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "info":
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  const refreshData = () => {
    setLastUpdate(new Date());
    // In real app, this would fetch fresh data from API
  };

  return (
    <DashboardLayout>
      <AnimatedBackground variant="minimal" className="opacity-5" />
      <div className="container mx-auto max-w-7xl relative z-10">
        {/* Header */}
        <div className="mb-8 animate-entrance">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <Activity className="h-8 w-8 text-green-600" />
                Monitoring Système 🖥️
              </h1>
              <p className="text-muted-foreground mt-1">
                Surveillance en temps réel de l'infrastructure et des performances
              </p>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Dernière mise à jour: {lastUpdate.toLocaleTimeString()}
              </span>
              <Button variant="outline" size="sm" onClick={refreshData}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Actualiser
              </Button>
              <Button size="sm">
                <Settings className="mr-2 h-4 w-4" />
                Configurer
              </Button>
            </div>
          </div>
        </div>

        {/* System Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <AnimatedStatsCard
            icon={<Server className="h-6 w-6" />}
            title="Serveur"
            value={systemMetrics.server.status}
            delay={0.1}
          />
          <AnimatedStatsCard
            icon={<Database className="h-6 w-6" />}
            title="Base de Données"
            value={systemMetrics.database.status}
            delay={0.2}
          />
          <AnimatedStatsCard
            icon={<Globe className="h-6 w-6" />}
            title="Application"
            value={systemMetrics.application.status}
            delay={0.3}
          />
          <AnimatedStatsCard
            icon={<Shield className="h-6 w-6" />}
            title="Sécurité"
            value={systemMetrics.security.status}
            delay={0.4}
          />
        </div>

        {/* Alerts */}
        {alerts.length > 0 && (
          <div className="mb-8 animate-entrance">
            <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Alertes Récentes
            </h2>
            <div className="space-y-3">
              {alerts.slice(0, 3).map((alert) => (
                <Alert key={alert.id} className={getStatusColor(alert.type)}>
                  <div className="flex items-start gap-3">
                    {getAlertIcon(alert.type)}
                    <div className="flex-1">
                      <AlertTitle className="text-sm font-medium">{alert.title}</AlertTitle>
                      <AlertDescription className="text-sm">{alert.message}</AlertDescription>
                      <p className="text-xs text-muted-foreground mt-1">{alert.timestamp}</p>
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="server">Serveur</TabsTrigger>
            <TabsTrigger value="database">Base de Données</TabsTrigger>
            <TabsTrigger value="security">Sécurité</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Real-time Metrics */}
              <AnimatedCard
                title="Métriques en Temps Réel"
                description="Performance système actuelle"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <AnimatedCircularProgress
                      value={systemMetrics.server.cpu}
                      size={80}
                      color="default"
                      showPercentage={true}
                    />
                    <p className="text-sm font-medium mt-2">CPU</p>
                  </div>
                  <div className="text-center">
                    <AnimatedCircularProgress
                      value={systemMetrics.server.memory}
                      size={80}
                      color="warning"
                      showPercentage={true}
                    />
                    <p className="text-sm font-medium mt-2">Mémoire</p>
                  </div>
                </div>
                
                <div className="space-y-3 mt-6">
                  <div className="flex justify-between text-sm">
                    <span>Stockage</span>
                    <span>{systemMetrics.server.storage}%</span>
                  </div>
                  <AnimatedProgress value={systemMetrics.server.storage} color="error" />
                  
                  <div className="flex justify-between text-sm">
                    <span>Réseau</span>
                    <span>{systemMetrics.server.network}%</span>
                  </div>
                  <AnimatedProgress value={systemMetrics.server.network} color="success" />
                </div>
              </AnimatedCard>

              {/* Application Metrics */}
              <AnimatedCard
                title="Métriques Application"
                description="Performance de l'application web"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <Users className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-blue-600">{systemMetrics.application.activeUsers}</p>
                      <p className="text-sm text-blue-600">Utilisateurs Actifs</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <Zap className="h-6 w-6 text-green-600 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-green-600">{systemMetrics.application.requestsPerMinute}</p>
                      <p className="text-sm text-green-600">Req/min</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Temps de réponse</span>
                      <span className={systemMetrics.application.responseTime > 200 ? "text-red-600" : "text-green-600"}>
                        {systemMetrics.application.responseTime}ms
                      </span>
                    </div>
                    <Progress value={Math.min((systemMetrics.application.responseTime / 500) * 100, 100)} />
                    
                    <div className="flex justify-between text-sm">
                      <span>Taux d'erreur</span>
                      <span className="text-green-600">{systemMetrics.application.errorRate}%</span>
                    </div>
                    <Progress value={systemMetrics.application.errorRate * 10} />
                  </div>
                </div>
              </AnimatedCard>

              {/* Performance History */}
              <AnimatedCard
                title="Historique des Performances"
                description="Évolution sur les dernières 24h"
                hoverEffect="tilt"
                entranceAnimation="slideUp"
                delay={0.3}
                className="lg:col-span-2"
              >
                <div className="space-y-4">
                  {performanceHistory.map((data, index) => (
                    <div key={data.time} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{data.time}</span>
                        <span className="text-muted-foreground">{data.users} utilisateurs</span>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span>CPU</span>
                            <span>{data.cpu}%</span>
                          </div>
                          <Progress value={data.cpu} className="h-2" />
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span>Mémoire</span>
                            <span>{data.memory}%</span>
                          </div>
                          <Progress value={data.memory} className="h-2" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Server Tab */}
          <TabsContent value="server">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AnimatedCard
                title="État du Serveur"
                description="Informations détaillées du serveur"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Statut</span>
                    <Badge className={getStatusColor(systemMetrics.server.status)}>
                      {systemMetrics.server.status}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Uptime</span>
                    <span className="text-sm">{systemMetrics.server.uptime}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Température</span>
                    <span className="text-sm">{systemMetrics.server.temperature}°C</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Charge système</span>
                    <span className="text-sm">{systemMetrics.server.load}</span>
                  </div>
                </div>
              </AnimatedCard>

              <AnimatedCard
                title="Ressources Système"
                description="Utilisation des ressources"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Cpu className="h-5 w-5 text-blue-600" />
                    <div className="flex-1">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Processeur</span>
                        <span>{systemMetrics.server.cpu}%</span>
                      </div>
                      <Progress value={systemMetrics.server.cpu} />
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <MemoryStick className="h-5 w-5 text-green-600" />
                    <div className="flex-1">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Mémoire RAM</span>
                        <span>{systemMetrics.server.memory}%</span>
                      </div>
                      <Progress value={systemMetrics.server.memory} />
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <HardDrive className="h-5 w-5 text-orange-600" />
                    <div className="flex-1">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Stockage</span>
                        <span>{systemMetrics.server.storage}%</span>
                      </div>
                      <Progress value={systemMetrics.server.storage} />
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <Wifi className="h-5 w-5 text-purple-600" />
                    <div className="flex-1">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Réseau</span>
                        <span>{systemMetrics.server.network}%</span>
                      </div>
                      <Progress value={systemMetrics.server.network} />
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Database Tab */}
          <TabsContent value="database">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AnimatedCard
                title="État de la Base de Données"
                description="Métriques de performance de la DB"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Statut</span>
                    <Badge className={getStatusColor(systemMetrics.database.status)}>
                      {systemMetrics.database.status}
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Connexions actives</span>
                      <span>{systemMetrics.database.connections}/{systemMetrics.database.maxConnections}</span>
                    </div>
                    <Progress value={(systemMetrics.database.connections / systemMetrics.database.maxConnections) * 100} />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Temps de requête moyen</span>
                    <span className="text-sm">{systemMetrics.database.queryTime}ms</span>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Taux de cache hit</span>
                      <span>{systemMetrics.database.cacheHitRate}%</span>
                    </div>
                    <Progress value={systemMetrics.database.cacheHitRate} />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Taille de la DB</span>
                    <span className="text-sm">{systemMetrics.database.size}</span>
                  </div>
                </div>
              </AnimatedCard>

              <AnimatedCard
                title="Sauvegardes et Maintenance"
                description="État des sauvegardes et opérations"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Dernière sauvegarde</span>
                    <Badge className="bg-green-100 text-green-800">
                      {systemMetrics.database.backupStatus}
                    </Badge>
                  </div>
                  
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">Sauvegarde automatique</span>
                    </div>
                    <p className="text-xs text-green-700 mt-1">
                      Dernière sauvegarde: Aujourd'hui à 02:00
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Prochaines maintenances</h4>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p>• Optimisation des index: Dimanche 02:00</p>
                      <p>• Nettoyage des logs: Lundi 01:00</p>
                      <p>• Mise à jour sécurité: Vendredi 23:00</p>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AnimatedCard
                title="État de la Sécurité"
                description="Monitoring de sécurité en temps réel"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Statut global</span>
                    <Badge className={getStatusColor(systemMetrics.security.status)}>
                      {systemMetrics.security.status}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Tentatives de connexion échouées</span>
                    <span className="text-sm">{systemMetrics.security.failedLogins}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">IPs bloquées</span>
                    <span className="text-sm">{systemMetrics.security.blockedIPs}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Vulnérabilités détectées</span>
                    <Badge className="bg-green-100 text-green-800">
                      {systemMetrics.security.vulnerabilities}
                    </Badge>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Expiration SSL</span>
                    <span className="text-sm">{systemMetrics.security.sslExpiry}</span>
                  </div>
                </div>
              </AnimatedCard>

              <AnimatedCard
                title="Dernières Analyses"
                description="Résultats des scans de sécurité"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-800">Scan de vulnérabilités</span>
                    </div>
                    <p className="text-xs text-green-700 mt-1">
                      Dernière analyse: {systemMetrics.security.lastScan}
                    </p>
                    <p className="text-xs text-green-700">Aucune vulnérabilité critique détectée</p>
                  </div>
                  
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">Firewall</span>
                    </div>
                    <p className="text-xs text-blue-700 mt-1">
                      Statut: Actif et opérationnel
                    </p>
                    <p className="text-xs text-blue-700">Dernière mise à jour des règles: Hier</p>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Prochaines tâches</h4>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p>• Renouvellement certificat SSL: Dans 6 mois</p>
                      <p>• Audit de sécurité: Dans 2 semaines</p>
                      <p>• Mise à jour pare-feu: Dans 3 jours</p>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default SystemMonitoring;
