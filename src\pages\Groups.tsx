
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Clock, Filter, Plus, Search, Users } from "lucide-react";
import { toast } from "sonner";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { useAuth } from "@/contexts/AuthContext";

interface WorkGroupMember {
  id: string;
  name: string;
  avatar?: string;
  role: string;
}

interface WorkGroup {
  id: string;
  name: string;
  description: string;
  course: string;
  members: WorkGroupMember[];
  maxMembers: number;
  tags: string[];
  lastActive: string;
  createdBy: WorkGroupMember;
}

const GroupsPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  
  // Mock work groups data
  const groups: WorkGroup[] = [
    {
      id: "g1",
      name: "Calculus Study Group",
      description: "A group focused on advanced calculus topics and problem-solving sessions",
      course: "Advanced Calculus 301",
      members: [
        { id: "u1", name: "Alex Johnson", role: "student" },
        { id: "u2", name: "Maria Garcia", avatar: "/placeholder.svg", role: "student" },
        { id: "u3", name: "David Kim", role: "student" },
        { id: "u4", name: "Sarah Williams", role: "teacher" }
      ],
      maxMembers: 8,
      tags: ["mathematics", "calculus", "study group"],
      lastActive: "2025-05-14T18:30:00Z",
      createdBy: { id: "u1", name: "Alex Johnson", role: "student" }
    },
    {
      id: "g2",
      name: "Physics Lab Partners",
      description: "Group for physics lab experiments and report collaboration",
      course: "Experimental Physics 202",
      members: [
        { id: "u2", name: "Maria Garcia", avatar: "/placeholder.svg", role: "student" },
        { id: "u5", name: "James Wilson", role: "student" },
        { id: "u6", name: "Emma Chen", avatar: "/placeholder.svg", role: "student" }
      ],
      maxMembers: 4,
      tags: ["physics", "laboratory", "experiments"],
      lastActive: "2025-05-13T14:15:00Z",
      createdBy: { id: "u2", name: "Maria Garcia", avatar: "/placeholder.svg", role: "student" }
    },
    {
      id: "g3",
      name: "Programming Project Team",
      description: "Collaborative team for the final programming project",
      course: "Advanced Software Development 405",
      members: [
        { id: "u3", name: "David Kim", role: "student" },
        { id: "u7", name: "Olivia Martinez", role: "student" },
        { id: "u8", name: "Michael Brown", avatar: "/placeholder.svg", role: "student" },
        { id: "u9", name: "Sophia Lee", role: "student" },
        { id: "u10", name: "Daniel Robinson", role: "student" }
      ],
      maxMembers: 5,
      tags: ["programming", "project", "team"],
      lastActive: "2025-05-15T09:45:00Z",
      createdBy: { id: "u3", name: "David Kim", role: "student" }
    },
    {
      id: "g4",
      name: "Literature Discussion Circle",
      description: "A group to discuss literary works and prepare for essays",
      course: "Comparative Literature 304",
      members: [
        { id: "u4", name: "Sarah Williams", avatar: "/placeholder.svg", role: "teacher" },
        { id: "u11", name: "Emma Taylor", role: "student" },
        { id: "u12", name: "Noah Garcia", avatar: "/placeholder.svg", role: "student" }
      ],
      maxMembers: 10,
      tags: ["literature", "discussion", "essays"],
      lastActive: "2025-05-10T16:20:00Z",
      createdBy: { id: "u4", name: "Sarah Williams", avatar: "/placeholder.svg", role: "teacher" }
    },
    {
      id: "g5",
      name: "Research Methods Workshop",
      description: "Collaborative group for practicing research methods and data analysis",
      course: "Research Methodology 501",
      members: [
        { id: "u5", name: "James Wilson", role: "student" },
        { id: "u13", name: "Ava Johnson", role: "student" },
        { id: "u14", name: "Ethan Smith", avatar: "/placeholder.svg", role: "student" },
        { id: "u15", name: "Isabella Brown", role: "student" },
        { id: "u16", name: "William Davis", avatar: "/placeholder.svg", role: "student" },
        { id: "u17", name: "Charlotte Rodriguez", role: "student" }
      ],
      maxMembers: 8,
      tags: ["research", "methodology", "data analysis"],
      lastActive: "2025-05-12T10:30:00Z",
      createdBy: { id: "u5", name: "James Wilson", role: "student" }
    }
  ];
  
  // Get filtered groups based on active tab and search query
  const getFilteredGroups = () => {
    let filteredGroups = [...groups];
    
    // Filter by tab
    if (activeTab === "my-groups") {
      filteredGroups = filteredGroups.filter(
        g => g.members.some(m => m.id === user?.id) || g.createdBy.id === user?.id
      );
    } else if (activeTab === "available") {
      filteredGroups = filteredGroups.filter(
        g => g.members.length < g.maxMembers && !g.members.some(m => m.id === user?.id)
      );
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredGroups = filteredGroups.filter(g => 
        g.name.toLowerCase().includes(query) || 
        g.description.toLowerCase().includes(query) ||
        g.course.toLowerCase().includes(query) ||
        g.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    return filteredGroups;
  };
  
  // Format date to relative time (e.g., "2 hours ago")
  const formatLastActive = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    // If older than a week, return the actual date
    return date.toLocaleDateString();
  };

  const handleCreateGroup = () => {
    toast.info("This feature is coming soon!");
    // In a real app, navigate to group creation form: navigate("/dashboard/groups/create");
  };

  const handleGroupClick = (groupId: string) => {
    // In a real app, navigate to group detail page
    toast.info(`Viewing group ${groupId}`);
    // navigate(`/dashboard/groups/${groupId}`);
  };

  const handleJoinGroup = (groupId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    toast.success("Request to join group sent!");
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Work Groups</h1>
            <p className="text-muted-foreground">
              Join or create groups to collaborate with other students and teachers
            </p>
          </div>
          <Button onClick={handleCreateGroup} className="mt-4 md:mt-0">
            <Plus className="mr-2 h-4 w-4" />
            Create Group
          </Button>
        </div>

        <div className="mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search groups..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Groups</TabsTrigger>
            <TabsTrigger value="my-groups">My Groups</TabsTrigger>
            <TabsTrigger value="available">Available to Join</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="m-0">
            {getFilteredGroups().length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getFilteredGroups().map((group) => (
                  <Card 
                    key={group.id}
                    onClick={() => handleGroupClick(group.id)}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                  >
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{group.name}</CardTitle>
                      <CardDescription>{group.course}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                        {group.description}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {group.tags.map(tag => (
                          <Badge key={tag} variant="secondary">{tag}</Badge>
                        ))}
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center">
                          <Users className="h-4 w-4 text-muted-foreground mr-1" />
                          <span>{group.members.length}/{group.maxMembers} members</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 text-muted-foreground mr-1" />
                          <span>Active {formatLastActive(group.lastActive)}</span>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="pt-2 border-t flex-col items-start">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="flex -space-x-2">
                          {group.members.slice(0, 3).map((member) => (
                            <Avatar key={member.id} className="h-6 w-6 border-2 border-background">
                              <AvatarImage src={member.avatar} />
                              <AvatarFallback className="text-xs">{member.name.charAt(0)}</AvatarFallback>
                            </Avatar>
                          ))}
                          {group.members.length > 3 && (
                            <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center text-xs">
                              +{group.members.length - 3}
                            </div>
                          )}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          Created by {group.createdBy.name}
                        </span>
                      </div>
                      {!group.members.some(m => m.id === user?.id) && group.members.length < group.maxMembers && (
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="w-full"
                          onClick={(e) => handleJoinGroup(group.id, e)}
                        >
                          <Users className="mr-2 h-4 w-4" />
                          Join Group
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg shadow">
                <Users className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">No groups found</h3>
                <p className="text-sm text-muted-foreground mt-1 text-center max-w-md">
                  {searchQuery 
                    ? "Try adjusting your search query" 
                    : activeTab === "my-groups"
                      ? "You haven't joined any groups yet"
                      : activeTab === "available"
                        ? "There are no available groups to join at the moment"
                        : "No groups have been created yet"}
                </p>
                <Button onClick={handleCreateGroup} className="mt-4">
                  Create a Group
                </Button>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="my-groups" className="m-0">
            {/* Content for My Groups tab - reusing the same rendering logic */}
          </TabsContent>
          
          <TabsContent value="available" className="m-0">
            {/* Content for Available to Join tab - reusing the same rendering logic */}
          </TabsContent>
        </Tabs>

        <div className="mt-6 flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious href="#" />
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#" isActive>1</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">2</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationLink href="#">3</PaginationLink>
              </PaginationItem>
              <PaginationItem>
                <PaginationNext href="#" />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default GroupsPage;
