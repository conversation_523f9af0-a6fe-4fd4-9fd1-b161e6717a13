import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import AnimatedBackground from "@/components/animations/AnimatedBackground";
import AnimatedCard, { AnimatedStatsCard } from "@/components/animations/AnimatedCard";
import { usePageEntrance } from "@/hooks/useGSAPAnimations";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  BookOpen,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Eye,
  FileText,
  PieChart,
  Activity,
  Clock,
  Award,
  MessageSquare,
  GraduationCap,
  Building,
  Target,
  Zap
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
// import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const AdminAnalytics = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedPeriod, setSelectedPeriod] = useState("month");
  const [selectedDepartment, setSelectedDepartment] = useState("all");

  // Add page entrance animations
  usePageEntrance([]);

  // Mock analytics data
  const analyticsData = {
    overview: {
      totalUsers: 1336,
      activeUsers: 892,
      newRegistrations: 47,
      retentionRate: 87.3,
      averageSessionTime: 45,
      totalCourses: 156,
      completedCourses: 1247,
      averageGrade: 82.5
    },
    growth: {
      userGrowth: [
        { month: "Jan", students: 980, teachers: 78, total: 1058 },
        { month: "Feb", students: 1045, teachers: 82, total: 1127 },
        { month: "Mar", students: 1123, teachers: 85, total: 1208 },
        { month: "Apr", students: 1189, teachers: 87, total: 1276 },
        { month: "May", students: 1247, teachers: 89, total: 1336 }
      ],
      courseGrowth: [
        { month: "Jan", created: 12, completed: 156 },
        { month: "Feb", created: 8, completed: 178 },
        { month: "Mar", created: 15, completed: 203 },
        { month: "Apr", created: 11, completed: 234 },
        { month: "May", created: 9, completed: 267 }
      ]
    },
    engagement: {
      dailyActiveUsers: [
        { day: "Lun", users: 756 },
        { day: "Mar", users: 823 },
        { day: "Mer", users: 891 },
        { day: "Jeu", users: 867 },
        { day: "Ven", users: 934 },
        { day: "Sam", users: 445 },
        { day: "Dim", users: 312 }
      ],
      topFeatures: [
        { feature: "Dashboard", usage: 95, users: 1268 },
        { feature: "Cours", usage: 87, users: 1162 },
        { feature: "Forum", usage: 73, users: 975 },
        { feature: "Assignments", usage: 68, users: 908 },
        { feature: "Study Planner", usage: 62, users: 828 },
        { feature: "Achievements", usage: 58, users: 775 }
      ]
    },
    performance: {
      departments: [
        { name: "Informatique", students: 456, avgGrade: 85.2, completion: 92 },
        { name: "Mathématiques", students: 389, avgGrade: 83.7, completion: 89 },
        { name: "Physique", students: 234, avgGrade: 81.4, completion: 87 },
        { name: "Chimie", students: 168, avgGrade: 84.1, completion: 91 }
      ],
      topCourses: [
        { name: "Développement Web", students: 145, rating: 4.8, completion: 94 },
        { name: "Data Science", students: 123, rating: 4.7, completion: 91 },
        { name: "Intelligence Artificielle", students: 98, rating: 4.9, completion: 89 },
        { name: "Cybersécurité", students: 87, rating: 4.6, completion: 93 },
        { name: "Mobile Development", students: 76, rating: 4.5, completion: 88 }
      ]
    },
    system: {
      serverMetrics: {
        cpu: 45,
        memory: 67,
        storage: 78,
        network: 23
      },
      errorRates: [
        { type: "4xx Errors", count: 23, percentage: 0.12 },
        { type: "5xx Errors", count: 8, percentage: 0.04 },
        { type: "Timeouts", count: 12, percentage: 0.06 }
      ],
      uptime: 99.97
    }
  };

  const recentReports = [
    {
      id: "1",
      title: "Rapport Mensuel - Mai 2024",
      type: "monthly",
      generatedAt: "2024-05-31",
      size: "2.3 MB",
      downloads: 15
    },
    {
      id: "2", 
      title: "Analyse des Performances Étudiantes",
      type: "performance",
      generatedAt: "2024-05-28",
      size: "1.8 MB",
      downloads: 23
    },
    {
      id: "3",
      title: "Rapport d'Utilisation des Fonctionnalités",
      type: "usage",
      generatedAt: "2024-05-25",
      size: "1.2 MB",
      downloads: 18
    }
  ];

  const getMetricColor = (value: number, threshold: { good: number; warning: number }) => {
    if (value >= threshold.good) return "text-green-600";
    if (value >= threshold.warning) return "text-yellow-600";
    return "text-red-600";
  };

  const generateReport = (type: string) => {
    // Simulate report generation
    console.log(`Generating ${type} report...`);
    // In real app, this would trigger backend report generation
  };

  return (
    <DashboardLayout>
      <AnimatedBackground variant="minimal" className="opacity-10" />
      <div className="container mx-auto max-w-7xl relative z-10">
        {/* Header */}
        <div className="mb-8 animate-entrance">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                Analytics & Rapports 📊
              </h1>
              <p className="text-muted-foreground mt-1">
                Analyses détaillées et rapports de performance de votre établissement
              </p>
            </div>
            <div className="flex gap-2">
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">Cette semaine</SelectItem>
                  <SelectItem value="month">Ce mois</SelectItem>
                  <SelectItem value="quarter">Ce trimestre</SelectItem>
                  <SelectItem value="year">Cette année</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Actualiser
              </Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Exporter
              </Button>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <AnimatedStatsCard
            icon={<Users className="h-6 w-6" />}
            title="Utilisateurs Actifs"
            value={analyticsData.overview.activeUsers}
            change={12.5}
            delay={0.1}
          />
          <AnimatedStatsCard
            icon={<TrendingUp className="h-6 w-6" />}
            title="Taux de Rétention"
            value={analyticsData.overview.retentionRate}
            change={3.2}
            delay={0.2}
          />
          <AnimatedStatsCard
            icon={<Clock className="h-6 w-6" />}
            title="Temps de Session Moyen"
            value={analyticsData.overview.averageSessionTime}
            change={-2.1}
            delay={0.3}
          />
          <AnimatedStatsCard
            icon={<Award className="h-6 w-6" />}
            title="Note Moyenne"
            value={analyticsData.overview.averageGrade}
            change={1.8}
            delay={0.4}
          />
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="users">Utilisateurs</TabsTrigger>
            <TabsTrigger value="courses">Cours</TabsTrigger>
            <TabsTrigger value="system">Système</TabsTrigger>
            <TabsTrigger value="reports">Rapports</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* User Growth Chart */}
              <AnimatedCard
                title="Croissance des Utilisateurs"
                description="Évolution du nombre d'utilisateurs sur 5 mois"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-4">
                  {analyticsData.growth.userGrowth.map((data, index) => (
                    <div key={data.month} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{data.month}</span>
                        <span className="font-medium">{data.total} utilisateurs</span>
                      </div>
                      <div className="flex gap-1">
                        <div className="flex-1 bg-blue-100 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${(data.students / data.total) * 100}%` }}
                          />
                        </div>
                        <div className="w-16 bg-green-100 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full transition-all duration-500"
                            style={{ width: `${(data.teachers / data.total) * 100}%` }}
                          />
                        </div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>{data.students} étudiants</span>
                        <span>{data.teachers} enseignants</span>
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCard>

              {/* Feature Usage */}
              <AnimatedCard
                title="Utilisation des Fonctionnalités"
                description="Popularité des différentes sections"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  {analyticsData.engagement.topFeatures.map((feature) => (
                    <div key={feature.feature} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium">{feature.feature}</span>
                        <span className="text-muted-foreground">{feature.users} utilisateurs</span>
                      </div>
                      <Progress value={feature.usage} className="h-2" />
                      <div className="text-xs text-muted-foreground text-right">
                        {feature.usage}% d'adoption
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCard>

              {/* Daily Active Users */}
              <AnimatedCard
                title="Utilisateurs Actifs par Jour"
                description="Activité de la semaine dernière"
                hoverEffect="tilt"
                entranceAnimation="slideUp"
                delay={0.3}
              >
                <div className="space-y-3">
                  {analyticsData.engagement.dailyActiveUsers.map((day) => (
                    <div key={day.day} className="flex items-center gap-3">
                      <span className="w-8 text-sm font-medium">{day.day}</span>
                      <div className="flex-1 bg-muted rounded-full h-6 relative">
                        <div 
                          className="bg-gradient-to-r from-blue-500 to-purple-600 h-6 rounded-full transition-all duration-700 flex items-center justify-end pr-2"
                          style={{ width: `${(day.users / 1000) * 100}%` }}
                        >
                          <span className="text-white text-xs font-medium">{day.users}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCard>

              {/* System Health */}
              <AnimatedCard
                title="État du Système"
                description="Métriques de performance en temps réel"
                hoverEffect="scale"
                entranceAnimation="slideUp"
                delay={0.4}
              >
                <div className="space-y-4">
                  {Object.entries(analyticsData.system.serverMetrics).map(([key, value]) => (
                    <div key={key} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="capitalize">{key}</span>
                        <span className={`font-medium ${getMetricColor(value, { good: 80, warning: 60 })}`}>
                          {value}%
                        </span>
                      </div>
                      <Progress 
                        value={value} 
                        className={`h-2 ${value > 80 ? 'text-red-500' : value > 60 ? 'text-yellow-500' : 'text-green-500'}`}
                      />
                    </div>
                  ))}
                  <div className="pt-2 border-t">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Uptime</span>
                      <Badge className="bg-green-100 text-green-800">
                        {analyticsData.system.uptime}%
                      </Badge>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AnimatedCard
                title="Répartition par Département"
                description="Nombre d'étudiants par département"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-4">
                  {analyticsData.performance.departments.map((dept) => (
                    <div key={dept.name} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium">{dept.name}</h4>
                        <Badge variant="outline">{dept.students} étudiants</Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Moyenne:</span>
                          <span className="ml-2 font-medium">{dept.avgGrade}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Réussite:</span>
                          <span className="ml-2 font-medium">{dept.completion}%</span>
                        </div>
                      </div>
                      <Progress value={dept.completion} className="mt-2 h-2" />
                    </div>
                  ))}
                </div>
              </AnimatedCard>

              <AnimatedCard
                title="Nouvelles Inscriptions"
                description="Tendance des inscriptions récentes"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary">{analyticsData.overview.newRegistrations}</div>
                    <p className="text-sm text-muted-foreground">nouvelles inscriptions ce mois</p>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Étudiants</span>
                      <span className="font-medium">42</span>
                    </div>
                    <Progress value={89} className="h-2" />
                    
                    <div className="flex justify-between text-sm">
                      <span>Enseignants</span>
                      <span className="font-medium">5</span>
                    </div>
                    <Progress value={11} className="h-2" />
                  </div>
                  
                  <div className="pt-3 border-t text-center">
                    <p className="text-sm text-green-600 font-medium">
                      +23% par rapport au mois dernier
                    </p>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Courses Tab */}
          <TabsContent value="courses">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AnimatedCard
                title="Cours les Plus Populaires"
                description="Top 5 des cours par nombre d'inscriptions"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-3">
                  {analyticsData.performance.topCourses.map((course, index) => (
                    <div key={course.name} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-bold">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">{course.name}</h4>
                        <p className="text-xs text-muted-foreground">
                          {course.students} étudiants • {course.completion}% de réussite
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-yellow-500">★</span>
                        <span className="text-sm font-medium">{course.rating}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCard>

              <AnimatedCard
                title="Statistiques des Cours"
                description="Vue d'ensemble de l'activité des cours"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <BookOpen className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-blue-600">{analyticsData.overview.totalCourses}</p>
                      <p className="text-sm text-blue-600">Cours Actifs</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <GraduationCap className="h-6 w-6 text-green-600 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-green-600">{analyticsData.overview.completedCourses}</p>
                      <p className="text-sm text-green-600">Cours Terminés</p>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Taux de completion moyen</span>
                      <span className="font-medium">89.2%</span>
                    </div>
                    <Progress value={89.2} className="h-2" />
                    
                    <div className="flex justify-between text-sm">
                      <span>Satisfaction moyenne</span>
                      <span className="font-medium">4.6/5</span>
                    </div>
                    <Progress value={92} className="h-2" />
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* System Tab */}
          <TabsContent value="system">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <AnimatedCard
                title="Métriques Serveur"
                description="Performance en temps réel"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="space-y-4">
                  {Object.entries(analyticsData.system.serverMetrics).map(([key, value]) => (
                    <div key={key} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="capitalize font-medium">{key}</span>
                        <span className={`font-bold ${getMetricColor(value, { good: 70, warning: 85 })}`}>
                          {value}%
                        </span>
                      </div>
                      <div className="relative">
                        <Progress value={value} className="h-3" />
                        {value > 85 && (
                          <div className="absolute right-0 top-0 h-3 w-3 bg-red-500 rounded-full animate-pulse" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCard>

              <AnimatedCard
                title="Erreurs et Incidents"
                description="Monitoring des erreurs système"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-4">
                  {analyticsData.system.errorRates.map((error) => (
                    <div key={error.type} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <h4 className="font-medium text-sm">{error.type}</h4>
                        <p className="text-xs text-muted-foreground">{error.percentage}% du trafic</p>
                      </div>
                      <Badge variant={error.count > 20 ? "destructive" : "secondary"}>
                        {error.count}
                      </Badge>
                    </div>
                  ))}
                  
                  <div className="pt-3 border-t">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Uptime Global</span>
                      <Badge className="bg-green-100 text-green-800">
                        {analyticsData.system.uptime}%
                      </Badge>
                    </div>
                  </div>
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports">
            <div className="space-y-6">
              {/* Report Generation */}
              <AnimatedCard
                title="Générer un Nouveau Rapport"
                description="Créez des rapports personnalisés selon vos besoins"
                hoverEffect="lift"
                entranceAnimation="slideUp"
                delay={0.1}
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col gap-2"
                    onClick={() => generateReport("monthly")}
                  >
                    <Calendar className="h-6 w-6 text-blue-600" />
                    <span className="font-medium">Rapport Mensuel</span>
                    <span className="text-xs text-muted-foreground">Statistiques complètes du mois</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col gap-2"
                    onClick={() => generateReport("performance")}
                  >
                    <TrendingUp className="h-6 w-6 text-green-600" />
                    <span className="font-medium">Performance</span>
                    <span className="text-xs text-muted-foreground">Analyse des performances</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-auto p-4 flex flex-col gap-2"
                    onClick={() => generateReport("usage")}
                  >
                    <Activity className="h-6 w-6 text-purple-600" />
                    <span className="font-medium">Utilisation</span>
                    <span className="text-xs text-muted-foreground">Rapport d'usage des fonctionnalités</span>
                  </Button>
                </div>
              </AnimatedCard>

              {/* Recent Reports */}
              <AnimatedCard
                title="Rapports Récents"
                description="Historique des rapports générés"
                hoverEffect="glow"
                entranceAnimation="slideUp"
                delay={0.2}
              >
                <div className="space-y-3">
                  {recentReports.map((report) => (
                    <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-blue-600" />
                        <div>
                          <h4 className="font-medium text-sm">{report.title}</h4>
                          <p className="text-xs text-muted-foreground">
                            Généré le {report.generatedAt} • {report.size} • {report.downloads} téléchargements
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </AnimatedCard>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default AdminAnalytics;
