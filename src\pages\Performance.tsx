import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  <PERSON><PERSON>hart, 
  Calendar,
  Target,
  Award,
  AlertTriangle,
  CheckCircle,
  Clock,
  BookOpen,
  Users,
  Star,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface SubjectPerformance {
  subject: string;
  currentGrade: number;
  previousGrade: number;
  trend: "up" | "down" | "stable";
  assignments: number;
  completedAssignments: number;
  averageScore: number;
  classAverage: number;
  rank: number;
  totalStudents: number;
}

interface WeeklyActivity {
  week: string;
  studyHours: number;
  assignmentsCompleted: number;
  forumPosts: number;
  quizzesTaken: number;
}

const Performance = () => {
  const { user } = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState("semester");

  // Mock performance data
  const [subjectPerformance] = useState<SubjectPerformance[]>([
    {
      subject: "Computer Science",
      currentGrade: 88.5,
      previousGrade: 85.2,
      trend: "up",
      assignments: 12,
      completedAssignments: 11,
      averageScore: 88.5,
      classAverage: 82.3,
      rank: 3,
      totalStudents: 45
    },
    {
      subject: "Mathematics",
      currentGrade: 92.1,
      previousGrade: 94.8,
      trend: "down",
      assignments: 10,
      completedAssignments: 10,
      averageScore: 92.1,
      classAverage: 87.6,
      rank: 2,
      totalStudents: 38
    },
    {
      subject: "Physics",
      currentGrade: 79.3,
      previousGrade: 79.1,
      trend: "stable",
      assignments: 8,
      completedAssignments: 7,
      averageScore: 79.3,
      classAverage: 75.8,
      rank: 8,
      totalStudents: 42
    },
    {
      subject: "English",
      currentGrade: 85.7,
      previousGrade: 82.4,
      trend: "up",
      assignments: 9,
      completedAssignments: 9,
      averageScore: 85.7,
      classAverage: 81.2,
      rank: 5,
      totalStudents: 40
    }
  ]);

  const [weeklyActivity] = useState<WeeklyActivity[]>([
    { week: "Week 1", studyHours: 25, assignmentsCompleted: 4, forumPosts: 8, quizzesTaken: 2 },
    { week: "Week 2", studyHours: 28, assignmentsCompleted: 3, forumPosts: 12, quizzesTaken: 3 },
    { week: "Week 3", studyHours: 22, assignmentsCompleted: 5, forumPosts: 6, quizzesTaken: 1 },
    { week: "Week 4", studyHours: 30, assignmentsCompleted: 4, forumPosts: 15, quizzesTaken: 4 },
  ]);

  const getOverallGPA = () => {
    const total = subjectPerformance.reduce((sum, subject) => sum + subject.currentGrade, 0);
    return (total / subjectPerformance.length).toFixed(1);
  };

  const getOverallRank = () => {
    const weightedRank = subjectPerformance.reduce((sum, subject) => 
      sum + (subject.rank / subject.totalStudents), 0
    );
    const averagePercentile = (weightedRank / subjectPerformance.length) * 100;
    return Math.round(100 - averagePercentile);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up": return <ArrowUp className="h-4 w-4 text-green-600" />;
      case "down": return <ArrowDown className="h-4 w-4 text-red-600" />;
      default: return <div className="h-4 w-4" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up": return "text-green-600";
      case "down": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  const getGradeColor = (grade: number) => {
    if (grade >= 90) return "text-green-600";
    if (grade >= 80) return "text-blue-600";
    if (grade >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const getCompletionRate = () => {
    const totalAssignments = subjectPerformance.reduce((sum, subject) => sum + subject.assignments, 0);
    const completedAssignments = subjectPerformance.reduce((sum, subject) => sum + subject.completedAssignments, 0);
    return Math.round((completedAssignments / totalAssignments) * 100);
  };

  const getStrengthsAndWeaknesses = () => {
    const sorted = [...subjectPerformance].sort((a, b) => b.currentGrade - a.currentGrade);
    return {
      strengths: sorted.slice(0, 2),
      weaknesses: sorted.slice(-2).reverse()
    };
  };

  const { strengths, weaknesses } = getStrengthsAndWeaknesses();

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-6xl">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <BarChart3 className="h-6 w-6" />
              Performance Dashboard
            </h1>
            <p className="text-muted-foreground">
              Track your academic progress and identify areas for improvement
            </p>
          </div>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="semester">This Semester</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Overall GPA</p>
                  <p className="text-2xl font-bold">{getOverallGPA()}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Class Rank</p>
                  <p className="text-2xl font-bold">Top {getOverallRank()}%</p>
                </div>
                <Award className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Completion Rate</p>
                  <p className="text-2xl font-bold">{getCompletionRate()}%</p>
                </div>
                <CheckCircle className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Study Hours</p>
                  <p className="text-2xl font-bold">105h</p>
                </div>
                <Clock className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="subjects" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="subjects">Subject Performance</TabsTrigger>
            <TabsTrigger value="trends">Trends & Analytics</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="goals">Goals & Targets</TabsTrigger>
          </TabsList>

          {/* Subject Performance */}
          <TabsContent value="subjects">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {subjectPerformance.map((subject) => (
                <Card key={subject.subject}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{subject.subject}</CardTitle>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(subject.trend)}
                        <span className={`text-sm font-medium ${getTrendColor(subject.trend)}`}>
                          {subject.trend === "up" ? "+" : subject.trend === "down" ? "-" : ""}
                          {Math.abs(subject.currentGrade - subject.previousGrade).toFixed(1)}
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Current Grade */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Current Grade</span>
                        <span className={`text-xl font-bold ${getGradeColor(subject.currentGrade)}`}>
                          {subject.currentGrade.toFixed(1)}%
                        </span>
                      </div>

                      {/* Progress vs Class Average */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>vs Class Average ({subject.classAverage.toFixed(1)}%)</span>
                          <span className={subject.currentGrade > subject.classAverage ? "text-green-600" : "text-red-600"}>
                            {subject.currentGrade > subject.classAverage ? "+" : ""}
                            {(subject.currentGrade - subject.classAverage).toFixed(1)}%
                          </span>
                        </div>
                        <Progress 
                          value={Math.min((subject.currentGrade / 100) * 100, 100)} 
                          className="h-2"
                        />
                      </div>

                      {/* Assignment Completion */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Assignments</span>
                        <span className="text-sm font-medium">
                          {subject.completedAssignments}/{subject.assignments}
                        </span>
                      </div>

                      {/* Class Rank */}
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">Class Rank</span>
                        <Badge variant="outline">
                          #{subject.rank} of {subject.totalStudents}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Trends & Analytics */}
          <TabsContent value="trends">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Weekly Activity</CardTitle>
                  <CardDescription>Your learning activity over the past 4 weeks</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {weeklyActivity.map((week) => (
                      <div key={week.week} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="font-medium">{week.week}</span>
                          <span className="text-muted-foreground">{week.studyHours}h study time</span>
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-xs">
                          <div className="flex items-center gap-1">
                            <CheckCircle className="h-3 w-3 text-green-600" />
                            <span>{week.assignmentsCompleted} assignments</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Users className="h-3 w-3 text-blue-600" />
                            <span>{week.forumPosts} forum posts</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <BookOpen className="h-3 w-3 text-purple-600" />
                            <span>{week.quizzesTaken} quizzes</span>
                          </div>
                        </div>
                        <Progress value={(week.studyHours / 35) * 100} className="h-1" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Grade Distribution</CardTitle>
                  <CardDescription>Your performance across all subjects</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {subjectPerformance.map((subject) => (
                      <div key={subject.subject} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm font-medium">{subject.subject}</span>
                          <span className={`text-sm font-bold ${getGradeColor(subject.currentGrade)}`}>
                            {subject.currentGrade.toFixed(1)}%
                          </span>
                        </div>
                        <Progress value={subject.currentGrade} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Insights */}
          <TabsContent value="insights">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-600" />
                    Your Strengths
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {strengths.map((subject) => (
                      <div key={subject.subject} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div>
                          <p className="font-medium">{subject.subject}</p>
                          <p className="text-sm text-muted-foreground">
                            Rank #{subject.rank} of {subject.totalStudents}
                          </p>
                        </div>
                        <span className="text-lg font-bold text-green-600">
                          {subject.currentGrade.toFixed(1)}%
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                    Areas for Improvement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {weaknesses.map((subject) => (
                      <div key={subject.subject} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                        <div>
                          <p className="font-medium">{subject.subject}</p>
                          <p className="text-sm text-muted-foreground">
                            {subject.assignments - subject.completedAssignments} pending assignments
                          </p>
                        </div>
                        <span className="text-lg font-bold text-orange-600">
                          {subject.currentGrade.toFixed(1)}%
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                    <Target className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Focus on Physics</p>
                      <p className="text-sm text-muted-foreground">
                        Your Physics grade is below your usual performance. Consider scheduling extra study time.
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Maintain Mathematics Excellence</p>
                      <p className="text-sm text-muted-foreground">
                        You're performing exceptionally well in Mathematics. Keep up the great work!
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                    <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <p className="font-medium">Increase Study Consistency</p>
                      <p className="text-sm text-muted-foreground">
                        Try to maintain consistent daily study hours for better retention.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Goals & Targets */}
          <TabsContent value="goals">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Semester Goals</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Overall GPA Target: 90.0</span>
                        <span className="text-sm text-muted-foreground">Current: {getOverallGPA()}</span>
                      </div>
                      <Progress value={(parseFloat(getOverallGPA()) / 90) * 100} />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Assignment Completion: 100%</span>
                        <span className="text-sm text-muted-foreground">Current: {getCompletionRate()}%</span>
                      </div>
                      <Progress value={getCompletionRate()} />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">Study Hours: 120h</span>
                        <span className="text-sm text-muted-foreground">Current: 105h</span>
                      </div>
                      <Progress value={(105 / 120) * 100} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Subject Targets</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {subjectPerformance.map((subject) => {
                      const target = 90;
                      const progress = (subject.currentGrade / target) * 100;
                      return (
                        <div key={subject.subject} className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm font-medium">{subject.subject}</span>
                            <span className="text-sm text-muted-foreground">
                              {subject.currentGrade.toFixed(1)}% / {target}%
                            </span>
                          </div>
                          <Progress value={Math.min(progress, 100)} />
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Performance;
