from django.urls import path
from . import views

urlpatterns = [
    # Questions
    path('questions/', views.QuestionListCreateView.as_view(), name='question_list_create'),
    path('questions/<uuid:id>/', views.QuestionDetailView.as_view(), name='question_detail'),
    
    # Answers
    path('questions/<uuid:question_id>/answers/', views.AnswerListCreateView.as_view(), name='answer_list_create'),
    path('answers/<uuid:id>/', views.AnswerDetailView.as_view(), name='answer_detail'),
    path('answers/<uuid:answer_id>/accept/', views.accept_answer, name='accept_answer'),
    
    # Voting
    path('vote/', views.VoteView.as_view(), name='vote'),
    
    # Tags
    path('tags/', views.TagListView.as_view(), name='tag_list'),
    path('tags/popular/', views.PopularTagsView.as_view(), name='popular_tags'),
    
    # Statistics and reputation
    path('stats/', views.ForumStatsView.as_view(), name='forum_stats'),
    path('reputation/<int:user_id>/', views.UserReputationView.as_view(), name='user_reputation'),
]
