import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { Progress } from '@/components/ui/progress';

interface AnimatedProgressProps {
  value: number;
  max?: number;
  className?: string;
  showPercentage?: boolean;
  animationDuration?: number;
  color?: 'default' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
  glowEffect?: boolean;
}

const AnimatedProgress = ({ 
  value, 
  max = 100, 
  className = '', 
  showPercentage = true,
  animationDuration = 1.5,
  color = 'default',
  size = 'md',
  glowEffect = false
}: AnimatedProgressProps) => {
  const progressRef = useRef<HTMLDivElement>(null);
  const percentageRef = useRef<HTMLSpanElement>(null);
  const glowRef = useRef<HTMLDivElement>(null);

  const percentage = Math.min((value / max) * 100, 100);

  useEffect(() => {
    if (!progressRef.current) return;

    const progressBar = progressRef.current.querySelector('[data-progress-indicator]') as HTMLElement;
    const percentageElement = percentageRef.current;
    const glowElement = glowRef.current;

    if (progressBar) {
      // Animate progress bar
      gsap.fromTo(progressBar, 
        { width: "0%" },
        { 
          width: `${percentage}%`,
          duration: animationDuration,
          ease: "power2.out"
        }
      );

      // Animate glow effect
      if (glowEffect && glowElement) {
        gsap.fromTo(glowElement,
          { opacity: 0, scale: 0.8 },
          {
            opacity: 0.6,
            scale: 1,
            duration: animationDuration * 0.5,
            ease: "power2.out",
            delay: animationDuration * 0.5
          }
        );
      }
    }

    // Animate percentage counter
    if (percentageElement) {
      const counter = { value: 0 };
      gsap.to(counter, {
        value: percentage,
        duration: animationDuration,
        ease: "power2.out",
        onUpdate: () => {
          percentageElement.textContent = `${Math.round(counter.value)}%`;
        }
      });
    }
  }, [value, max, animationDuration, percentage, glowEffect]);

  const getColorClasses = () => {
    switch (color) {
      case 'success':
        return 'bg-green-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-primary';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'h-2';
      case 'lg':
        return 'h-6';
      default:
        return 'h-4';
    }
  };

  const getGlowColor = () => {
    switch (color) {
      case 'success':
        return 'shadow-green-500/50';
      case 'warning':
        return 'shadow-yellow-500/50';
      case 'error':
        return 'shadow-red-500/50';
      default:
        return 'shadow-primary/50';
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div 
        ref={progressRef}
        className={`relative w-full ${getSizeClasses()} bg-muted rounded-full overflow-hidden`}
      >
        {/* Glow effect */}
        {glowEffect && (
          <div 
            ref={glowRef}
            className={`absolute inset-0 ${getColorClasses()} rounded-full blur-sm ${getGlowColor()} shadow-lg opacity-0`}
          />
        )}
        
        {/* Progress bar */}
        <div 
          data-progress-indicator
          className={`h-full ${getColorClasses()} rounded-full transition-all duration-300 relative overflow-hidden`}
          style={{ width: '0%' }}
        >
          {/* Shimmer effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
        </div>
      </div>
      
      {/* Percentage display */}
      {showPercentage && (
        <span 
          ref={percentageRef}
          className="absolute right-0 top-1/2 transform -translate-y-1/2 text-sm font-medium text-muted-foreground ml-2"
        >
          0%
        </span>
      )}
    </div>
  );
};

// Animated circular progress component
interface AnimatedCircularProgressProps {
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  color?: 'default' | 'success' | 'warning' | 'error';
  showPercentage?: boolean;
  animationDuration?: number;
}

export const AnimatedCircularProgress = ({
  value,
  max = 100,
  size = 120,
  strokeWidth = 8,
  className = '',
  color = 'default',
  showPercentage = true,
  animationDuration = 2
}: AnimatedCircularProgressProps) => {
  const circleRef = useRef<SVGCircleElement>(null);
  const percentageRef = useRef<HTMLSpanElement>(null);

  const percentage = Math.min((value / max) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;

  useEffect(() => {
    if (!circleRef.current) return;

    const circle = circleRef.current;
    const percentageElement = percentageRef.current;

    // Set initial state
    gsap.set(circle, { strokeDasharray: circumference, strokeDashoffset: circumference });

    // Animate circle
    gsap.to(circle, {
      strokeDashoffset: circumference - (percentage / 100) * circumference,
      duration: animationDuration,
      ease: "power2.out"
    });

    // Animate percentage
    if (percentageElement) {
      const counter = { value: 0 };
      gsap.to(counter, {
        value: percentage,
        duration: animationDuration,
        ease: "power2.out",
        onUpdate: () => {
          percentageElement.textContent = `${Math.round(counter.value)}%`;
        }
      });
    }
  }, [value, max, percentage, circumference, animationDuration]);

  const getStrokeColor = () => {
    switch (color) {
      case 'success':
        return 'stroke-green-500';
      case 'warning':
        return 'stroke-yellow-500';
      case 'error':
        return 'stroke-red-500';
      default:
        return 'stroke-primary';
    }
  };

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-muted"
        />
        
        {/* Progress circle */}
        <circle
          ref={circleRef}
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeLinecap="round"
          className={getStrokeColor()}
        />
      </svg>
      
      {/* Percentage display */}
      {showPercentage && (
        <span 
          ref={percentageRef}
          className="absolute text-lg font-bold text-foreground"
        >
          0%
        </span>
      )}
    </div>
  );
};

export default AnimatedProgress;
