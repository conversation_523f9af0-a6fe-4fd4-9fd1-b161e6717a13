import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";

const Dashboard = () => {
  const { user } = useAuth();
  
  console.log("Dashboard rendering, user:", user);
  
  return (
    <DashboardLayout>
      <div className="p-8">
        <div className="bg-card border rounded-lg p-6 shadow-sm">
          <h1 className="text-3xl font-bold mb-4 text-foreground">
            🎓 Campus Connect Dashboard
          </h1>
          <p className="text-muted-foreground mb-6">
            Bienvenue {user?.firstName || 'Utilisateur'} ! Rôle actuel : <strong>{user?.role || 'student'}</strong>
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                🎓 Dashboard Étudiant
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Suivi des cours, notes et progression académique
              </p>
              <button 
                onClick={() => console.log("Student dashboard clicked")}
                className="mt-3 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Voir Dashboard
              </button>
            </div>
            
            <div className="p-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                👨‍🏫 Dashboard Enseignant
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                Gestion des cours et suivi des étudiants
              </p>
              <button 
                onClick={() => console.log("Teacher dashboard clicked")}
                className="mt-3 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
              >
                Voir Dashboard
              </button>
            </div>
            
            <div className="p-6 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
              <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">
                🛡️ Dashboard Admin
              </h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                Analytics, rapports et monitoring système
              </p>
              <button 
                onClick={() => console.log("Admin dashboard clicked")}
                className="mt-3 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"
              >
                Voir Dashboard
              </button>
            </div>
          </div>
          
          <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
              🔧 Informations de Debug
            </h4>
            <ul className="text-sm text-yellow-800 dark:text-yellow-200 space-y-1">
              <li>• Utilisateur connecté : {user?.firstName} {user?.lastName}</li>
              <li>• Email : {user?.email}</li>
              <li>• Rôle : {user?.role}</li>
              <li>• Dashboard rendu avec succès ✅</li>
              <li>• DashboardLayout fonctionne ✅</li>
            </ul>
          </div>
          
          <div className="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <h4 className="font-medium text-green-900 dark:text-green-100 mb-2">
              ✅ Tests Réussis
            </h4>
            <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
              <li>• React fonctionne</li>
              <li>• DashboardLayout s'affiche</li>
              <li>• Mode sombre adaptatif</li>
              <li>• Styles Tailwind appliqués</li>
              <li>• AuthContext accessible</li>
            </ul>
          </div>
          
          <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h4 className="font-medium text-red-900 dark:text-red-100 mb-2">
              🚨 Prochaines Étapes
            </h4>
            <ul className="text-sm text-red-800 dark:text-red-200 space-y-1">
              <li>• Si vous voyez cette page, le problème de layout est résolu !</li>
              <li>• Utilisez le RoleSwitcher dans la sidebar pour changer de rôle</li>
              <li>• Testez le mode sombre avec le bouton dans la sidebar</li>
              <li>• Naviguez vers les autres pages pour vérifier qu'elles fonctionnent</li>
            </ul>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
