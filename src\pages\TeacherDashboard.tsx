import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import AnimatedBackground from "@/components/animations/AnimatedBackground";
import AnimatedCard, { AnimatedStatsCard } from "@/components/animations/AnimatedCard";
import { usePageEntrance } from "@/hooks/useGSAPAnimations";
import { 
  BookOpen, 
  Users, 
  ClipboardCheck, 
  MessageSquare,
  Calendar,
  TrendingUp,
  Clock,
  Award,
  FileText,
  Plus,
  Eye,
  Edit,
  GraduationCap,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Star,
  Coffee
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

const TeacherDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");

  // Add page entrance animations
  usePageEntrance([]);

  // Mock teacher data
  const teacherStats = {
    totalStudents: 156,
    activeCourses: 4,
    pendingGrading: 23,
    forumQuestions: 8,
    averageGrade: 82.5,
    attendanceRate: 94,
    completionRate: 87,
    satisfactionScore: 4.6
  };

  const myCourses = [
    {
      id: "1",
      name: "Développement Web Avancé",
      code: "CS-301",
      students: 45,
      progress: 75,
      nextClass: "2024-01-18 09:00",
      pendingAssignments: 8,
      averageGrade: 85.2
    },
    {
      id: "2",
      name: "Base de Données",
      code: "CS-250",
      students: 38,
      progress: 60,
      nextClass: "2024-01-18 14:00",
      pendingAssignments: 12,
      averageGrade: 78.9
    },
    {
      id: "3",
      name: "Algorithmes et Structures",
      code: "CS-200",
      students: 42,
      progress: 45,
      nextClass: "2024-01-19 10:00",
      pendingAssignments: 3,
      averageGrade: 81.7
    },
    {
      id: "4",
      name: "Projet Final",
      code: "CS-400",
      students: 31,
      progress: 30,
      nextClass: "2024-01-19 15:00",
      pendingAssignments: 0,
      averageGrade: 88.4
    }
  ];

  const recentActivities = [
    {
      id: "1",
      type: "assignment_submitted",
      message: "15 nouveaux devoirs soumis pour CS-301",
      timestamp: "Il y a 1 heure",
      course: "Développement Web Avancé"
    },
    {
      id: "2",
      type: "question_posted",
      message: "Nouvelle question sur le forum CS-250",
      timestamp: "Il y a 2 heures",
      course: "Base de Données"
    },
    {
      id: "3",
      type: "grade_request",
      message: "Demande de révision de note - CS-200",
      timestamp: "Il y a 4 heures",
      course: "Algorithmes et Structures"
    },
    {
      id: "4",
      type: "attendance_alert",
      message: "3 absences consécutives détectées",
      timestamp: "Il y a 6 heures",
      course: "Développement Web Avancé"
    }
  ];

  const upcomingClasses = [
    {
      id: "1",
      time: "09:00",
      course: "Développement Web Avancé",
      room: "Lab 3",
      topic: "React Hooks Avancés",
      students: 45
    },
    {
      id: "2",
      time: "14:00",
      course: "Base de Données",
      room: "Salle 201",
      topic: "Optimisation des Requêtes",
      students: 38
    },
    {
      id: "3",
      time: "16:00",
      course: "Consultation",
      room: "Bureau 15",
      topic: "Heures de bureau",
      students: 0
    }
  ];

  const topStudents = [
    { name: "Emma Wilson", course: "CS-301", grade: 95.2, avatar: "/placeholder.svg" },
    { name: "Lucas Martin", course: "CS-250", grade: 92.8, avatar: "/placeholder.svg" },
    { name: "Sophie Chen", course: "CS-200", grade: 91.5, avatar: "/placeholder.svg" },
    { name: "Thomas Brown", course: "CS-400", grade: 90.7, avatar: "/placeholder.svg" },
    { name: "Olivia Davis", course: "CS-301", grade: 89.9, avatar: "/placeholder.svg" }
  ];

  const quickActions = [
    {
      title: "Créer un devoir",
      description: "Nouveau devoir ou examen",
      icon: <Plus className="h-5 w-5" />,
      action: () => navigate("/dashboard/assignments/create"),
      color: "bg-blue-500"
    },
    {
      title: "Noter les devoirs",
      description: `${teacherStats.pendingGrading} en attente`,
      icon: <ClipboardCheck className="h-5 w-5" />,
      action: () => navigate("/dashboard/grading"),
      color: "bg-green-500"
    },
    {
      title: "Forum Q&A",
      description: `${teacherStats.forumQuestions} questions`,
      icon: <MessageSquare className="h-5 w-5" />,
      action: () => navigate("/dashboard/forum"),
      color: "bg-purple-500"
    },
    {
      title: "Analytics",
      description: "Performance des étudiants",
      icon: <BarChart3 className="h-5 w-5" />,
      action: () => navigate("/dashboard/analytics"),
      color: "bg-orange-500"
    }
  ];

  return (
    <DashboardLayout>
      <AnimatedBackground variant="geometric" className="opacity-15" />
      <div className="container mx-auto max-w-7xl relative z-10">
        {/* Header */}
        <div className="mb-8 animate-entrance">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold flex items-center gap-2">
                <GraduationCap className="h-8 w-8 text-blue-600" />
                Bonjour, Prof. {user?.lastName} ! 👨‍🏫
              </h1>
              <p className="text-muted-foreground mt-1">
                Gérez vos cours et suivez les progrès de vos étudiants
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Note de satisfaction</p>
              <div className="flex items-center gap-1">
                <Star className="h-5 w-5 text-yellow-500 fill-current" />
                <span className="text-2xl font-bold text-primary">{teacherStats.satisfactionScore}</span>
                <span className="text-sm text-muted-foreground">/5</span>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <AnimatedStatsCard
            icon={<Users className="h-6 w-6" />}
            title="Total Étudiants"
            value={teacherStats.totalStudents}
            delay={0.1}
          />
          <AnimatedStatsCard
            icon={<BookOpen className="h-6 w-6" />}
            title="Cours Actifs"
            value={teacherStats.activeCourses}
            delay={0.2}
          />
          <AnimatedStatsCard
            icon={<ClipboardCheck className="h-6 w-6" />}
            title="À Noter"
            value={teacherStats.pendingGrading}
            delay={0.3}
          />
          <AnimatedStatsCard
            icon={<TrendingUp className="h-6 w-6" />}
            title="Moyenne Générale"
            value={teacherStats.averageGrade}
            delay={0.4}
          />
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="courses">Mes Cours</TabsTrigger>
            <TabsTrigger value="students">Étudiants</TabsTrigger>
            <TabsTrigger value="schedule">Planning</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-6">
                {/* Quick Actions */}
                <AnimatedCard
                  title="Actions Rapides"
                  description="Accès direct à vos outils d'enseignement"
                  hoverEffect="lift"
                  entranceAnimation="slideUp"
                  delay={0.1}
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {quickActions.map((action, index) => (
                      <div
                        key={index}
                        onClick={action.action}
                        className="p-4 rounded-lg border cursor-pointer hover:shadow-md transition-all duration-200 hover:scale-105"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${action.color} text-white`}>
                            {action.icon}
                          </div>
                          <div>
                            <h4 className="font-medium">{action.title}</h4>
                            <p className="text-sm text-muted-foreground">{action.description}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </AnimatedCard>

                {/* Recent Activities */}
                <AnimatedCard
                  title="Activités Récentes"
                  description="Dernières interactions avec vos cours"
                  hoverEffect="glow"
                  entranceAnimation="slideUp"
                  delay={0.2}
                >
                  <div className="space-y-3">
                    {recentActivities.map((activity) => (
                      <div key={activity.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        <div className="flex-1">
                          <p className="font-medium text-sm">{activity.message}</p>
                          <p className="text-xs text-muted-foreground">
                            {activity.course} • {activity.timestamp}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </AnimatedCard>

                {/* Course Performance */}
                <AnimatedCard
                  title="Performance des Cours"
                  description="Aperçu des statistiques par cours"
                  hoverEffect="tilt"
                  entranceAnimation="slideUp"
                  delay={0.3}
                >
                  <div className="space-y-4">
                    {myCourses.slice(0, 3).map((course) => (
                      <div key={course.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium">{course.name}</h4>
                            <p className="text-sm text-muted-foreground">{course.code} • {course.students} étudiants</p>
                          </div>
                          <Badge variant="outline">
                            Moy: {course.averageGrade}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progression du cours</span>
                            <span>{course.progress}%</span>
                          </div>
                          <Progress value={course.progress} className="h-2" />
                        </div>
                      </div>
                    ))}
                  </div>
                </AnimatedCard>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Today's Schedule */}
                <AnimatedCard
                  title="Planning d'Aujourd'hui"
                  description="Vos cours et rendez-vous"
                  hoverEffect="scale"
                  entranceAnimation="slideLeft"
                  delay={0.2}
                >
                  <div className="space-y-3">
                    {upcomingClasses.map((class_) => (
                      <div key={class_.id} className="p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{class_.time}</span>
                          <Badge variant="outline">{class_.room}</Badge>
                        </div>
                        <h4 className="font-medium text-sm">{class_.course}</h4>
                        <p className="text-xs text-muted-foreground">{class_.topic}</p>
                        {class_.students > 0 && (
                          <p className="text-xs text-muted-foreground mt-1">
                            {class_.students} étudiants
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </AnimatedCard>

                {/* Top Students */}
                <AnimatedCard
                  title="Meilleurs Étudiants"
                  description="Top 5 par performance"
                  hoverEffect="glow"
                  entranceAnimation="slideLeft"
                  delay={0.3}
                >
                  <div className="space-y-3">
                    {topStudents.map((student, index) => (
                      <div key={student.name} className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs font-bold">
                          {index + 1}
                        </div>
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={student.avatar} />
                          <AvatarFallback>{student.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="font-medium text-sm">{student.name}</p>
                          <p className="text-xs text-muted-foreground">{student.course}</p>
                        </div>
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          {student.grade}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                </AnimatedCard>

                {/* Quick Stats */}
                <AnimatedCard
                  title="Statistiques Rapides"
                  hoverEffect="lift"
                  entranceAnimation="slideLeft"
                  delay={0.4}
                >
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Taux de présence</span>
                      <span className="font-medium">{teacherStats.attendanceRate}%</span>
                    </div>
                    <Progress value={teacherStats.attendanceRate} className="h-2" />
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Taux de réussite</span>
                      <span className="font-medium">{teacherStats.completionRate}%</span>
                    </div>
                    <Progress value={teacherStats.completionRate} className="h-2" />
                  </div>
                </AnimatedCard>
              </div>
            </div>
          </TabsContent>

          {/* Courses Tab */}
          <TabsContent value="courses">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {myCourses.map((course) => (
                <AnimatedCard
                  key={course.id}
                  hoverEffect="lift"
                  entranceAnimation="slideUp"
                  delay={0.1}
                >
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="font-semibold">{course.name}</h3>
                        <p className="text-sm text-muted-foreground">{course.code}</p>
                      </div>
                      <Badge variant="outline">{course.students} étudiants</Badge>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span>Progression</span>
                        <span>{course.progress}%</span>
                      </div>
                      <Progress value={course.progress} />
                      
                      <div className="flex justify-between text-sm">
                        <span>Moyenne de classe</span>
                        <span className="font-medium">{course.averageGrade}</span>
                      </div>
                      
                      <div className="flex justify-between text-sm">
                        <span>Devoirs en attente</span>
                        <Badge variant={course.pendingAssignments > 0 ? "destructive" : "secondary"}>
                          {course.pendingAssignments}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button size="sm" variant="outline">
                        <Eye className="mr-2 h-4 w-4" />
                        Voir
                      </Button>
                      <Button size="sm" variant="outline">
                        <Edit className="mr-2 h-4 w-4" />
                        Modifier
                      </Button>
                    </div>
                  </div>
                </AnimatedCard>
              ))}
            </div>
          </TabsContent>

          {/* Students Tab */}
          <TabsContent value="students">
            <AnimatedCard
              title="Gestion des Étudiants"
              description="Vue d'ensemble de vos étudiants"
              hoverEffect="lift"
              entranceAnimation="fadeIn"
              delay={0.1}
            >
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Gestion des Étudiants</h3>
                <p className="text-muted-foreground mb-4">
                  Interface de gestion des étudiants en développement
                </p>
                <Button>
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Voir les Analytics
                </Button>
              </div>
            </AnimatedCard>
          </TabsContent>

          {/* Schedule Tab */}
          <TabsContent value="schedule">
            <AnimatedCard
              title="Planning Complet"
              description="Votre emploi du temps détaillé"
              hoverEffect="lift"
              entranceAnimation="fadeIn"
              delay={0.1}
            >
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Planning Détaillé</h3>
                <p className="text-muted-foreground mb-4">
                  Calendrier complet avec tous vos cours et événements
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Ajouter un Événement
                </Button>
              </div>
            </AnimatedCard>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default TeacherDashboard;
