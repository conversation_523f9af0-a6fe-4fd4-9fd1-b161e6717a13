import { useRef, useEffect, ReactNode } from 'react';
import { gsap } from 'gsap';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface AnimatedCardProps {
  children?: ReactNode;
  title?: string;
  description?: string;
  className?: string;
  hoverEffect?: 'lift' | 'glow' | 'tilt' | 'scale' | 'none';
  entranceAnimation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale' | 'none';
  delay?: number;
  onClick?: () => void;
}

const AnimatedCard = ({
  children,
  title,
  description,
  className = '',
  hoverEffect = 'lift',
  entranceAnimation = 'fadeIn',
  delay = 0,
  onClick
}: AnimatedCardProps) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const card = cardRef.current;
    if (!card) return;

    // Entrance animation
    if (entranceAnimation !== 'none') {
      performEntranceAnimation(card, entranceAnimation, delay);
    }

    // Hover effects
    if (hoverEffect !== 'none') {
      setupHoverEffects(card, hoverEffect);
    }

    return () => {
      gsap.killTweensOf(card);
    };
  }, [entranceAnimation, hoverEffect, delay]);

  const performEntranceAnimation = (element: HTMLElement, animation: string, delay: number) => {
    switch (animation) {
      case 'fadeIn':
        gsap.fromTo(element,
          { opacity: 0, y: 20 },
          { opacity: 1, y: 0, duration: 0.6, delay, ease: "power2.out" }
        );
        break;
      case 'slideUp':
        gsap.fromTo(element,
          { opacity: 0, y: 50 },
          { opacity: 1, y: 0, duration: 0.8, delay, ease: "back.out(1.7)" }
        );
        break;
      case 'slideLeft':
        gsap.fromTo(element,
          { opacity: 0, x: 50 },
          { opacity: 1, x: 0, duration: 0.6, delay, ease: "power2.out" }
        );
        break;
      case 'slideRight':
        gsap.fromTo(element,
          { opacity: 0, x: -50 },
          { opacity: 1, x: 0, duration: 0.6, delay, ease: "power2.out" }
        );
        break;
      case 'scale':
        gsap.fromTo(element,
          { opacity: 0, scale: 0.8 },
          { opacity: 1, scale: 1, duration: 0.6, delay, ease: "back.out(1.7)" }
        );
        break;
    }
  };

  const setupHoverEffects = (element: HTMLElement, effect: string) => {
    let hoverTween: gsap.core.Tween | null = null;

    const handleMouseEnter = (e: MouseEvent) => {
      if (hoverTween) hoverTween.kill();

      switch (effect) {
        case 'lift':
          hoverTween = gsap.to(element, {
            y: -8,
            scale: 1.02,
            boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
            duration: 0.3,
            ease: "power2.out"
          });
          break;
        case 'glow':
          hoverTween = gsap.to(element, {
            boxShadow: "0 0 30px rgba(59, 130, 246, 0.3)",
            scale: 1.01,
            duration: 0.3,
            ease: "power2.out"
          });
          break;
        case 'tilt':
          const rect = element.getBoundingClientRect();
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;
          const rotateX = (e.clientY - centerY) / 10;
          const rotateY = (centerX - e.clientX) / 10;
          
          hoverTween = gsap.to(element, {
            rotationX: rotateX,
            rotationY: rotateY,
            scale: 1.05,
            duration: 0.3,
            ease: "power2.out",
            transformPerspective: 1000
          });
          break;
        case 'scale':
          hoverTween = gsap.to(element, {
            scale: 1.05,
            duration: 0.3,
            ease: "power2.out"
          });
          break;
      }
    };

    const handleMouseLeave = () => {
      if (hoverTween) hoverTween.kill();
      
      hoverTween = gsap.to(element, {
        y: 0,
        scale: 1,
        rotationX: 0,
        rotationY: 0,
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
        duration: 0.3,
        ease: "power2.out"
      });
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (effect === 'tilt') {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        const rotateX = (e.clientY - centerY) / 20;
        const rotateY = (centerX - e.clientX) / 20;
        
        gsap.to(element, {
          rotationX: rotateX,
          rotationY: rotateY,
          duration: 0.1,
          ease: "power2.out"
        });
      }
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    element.addEventListener('mousemove', handleMouseMove);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      element.removeEventListener('mousemove', handleMouseMove);
    };
  };

  return (
    <Card 
      ref={cardRef}
      className={`cursor-pointer transition-all duration-300 ${className}`}
      onClick={onClick}
    >
      <div ref={contentRef}>
        {(title || description) && (
          <CardHeader>
            {title && <CardTitle>{title}</CardTitle>}
            {description && <CardDescription>{description}</CardDescription>}
          </CardHeader>
        )}
        {children && (
          <CardContent>
            {children}
          </CardContent>
        )}
      </div>
    </Card>
  );
};

// Specialized animated card for achievements
export const AnimatedAchievementCard = ({ 
  achievement, 
  delay = 0,
  onClick 
}: { 
  achievement: any; 
  delay?: number;
  onClick?: () => void;
}) => {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const card = cardRef.current;
    if (!card) return;

    // Special entrance animation for achievements
    gsap.fromTo(card,
      { 
        opacity: 0, 
        scale: 0.8, 
        rotationY: -90 
      },
      { 
        opacity: 1, 
        scale: 1, 
        rotationY: 0,
        duration: 0.8,
        delay,
        ease: "back.out(1.7)"
      }
    );

    // Floating animation
    gsap.to(card, {
      y: -5,
      duration: 2,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1,
      delay: delay + 1
    });

  }, [delay]);

  const getRarityGlow = (rarity: string) => {
    switch (rarity) {
      case 'legendary':
        return 'shadow-yellow-500/30';
      case 'epic':
        return 'shadow-purple-500/30';
      case 'rare':
        return 'shadow-blue-500/30';
      default:
        return 'shadow-gray-500/20';
    }
  };

  return (
    <Card 
      ref={cardRef}
      className={`relative overflow-hidden cursor-pointer transition-all duration-300 hover:${getRarityGlow(achievement.rarity)} hover:shadow-lg`}
      onClick={onClick}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/5" />
      <CardContent className="p-4 relative">
        <div className="text-center">
          <div className="text-3xl mb-2">{achievement.icon}</div>
          <h3 className="font-semibold text-sm">{achievement.title}</h3>
          <p className="text-xs text-muted-foreground mt-1">{achievement.description}</p>
        </div>
      </CardContent>
    </Card>
  );
};

// Animated stats card
export const AnimatedStatsCard = ({ 
  icon, 
  title, 
  value, 
  change, 
  delay = 0 
}: { 
  icon: ReactNode; 
  title: string; 
  value: number; 
  change?: number;
  delay?: number;
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const valueRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    const card = cardRef.current;
    const valueElement = valueRef.current;

    if (card) {
      gsap.fromTo(card,
        { opacity: 0, y: 30 },
        { opacity: 1, y: 0, duration: 0.6, delay, ease: "power2.out" }
      );
    }

    if (valueElement) {
      const counter = { value: 0 };
      gsap.to(counter, {
        value,
        duration: 1.5,
        delay: delay + 0.3,
        ease: "power2.out",
        onUpdate: () => {
          valueElement.textContent = Math.round(counter.value).toString();
        }
      });
    }
  }, [value, delay]);

  return (
    <AnimatedCard 
      ref={cardRef}
      hoverEffect="lift"
      entranceAnimation="none"
      className="text-center"
    >
      <div className="flex items-center justify-center mb-2 text-primary">
        {icon}
      </div>
      <div>
        <span ref={valueRef} className="text-2xl font-bold">0</span>
        {change !== undefined && (
          <div className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {change >= 0 ? '+' : ''}{change}%
          </div>
        )}
      </div>
      <p className="text-sm text-muted-foreground mt-1">{title}</p>
    </AnimatedCard>
  );
};

export default AnimatedCard;
