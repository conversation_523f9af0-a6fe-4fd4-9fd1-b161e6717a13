
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { ThemeProvider } from "@/components/theme/theme-provider";
import PrivateRoute from "@/components/PrivateRoute";

// Pages
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";
import Dashboard from "./pages/Dashboard";
import Calendar from "./pages/Calendar";
import Profile from "./pages/Profile";
import Chat from "./pages/Chat";
import News from "./pages/News";
import Resources from "./pages/Resources";
import Sections from "./pages/Sections";
import Forum from "./pages/Forum";
import Groups from "./pages/Groups";
import Assignments from "./pages/Assignments";
import Settings from "./pages/Settings";
import Notifications from "./pages/Notifications";
import AskQuestion from "./pages/AskQuestion";
import QuestionDetail from "./pages/QuestionDetail";
import StudyPlanner from "./pages/StudyPlanner";
import Achievements from "./pages/Achievements";
import PeerReview from "./pages/PeerReview";
import Performance from "./pages/Performance";
import GPACalculator from "./pages/GPACalculator";
import AdminAnalytics from "./pages/AdminAnalytics";
import AdminReports from "./pages/AdminReports";
import SystemMonitoring from "./pages/SystemMonitoring";
import SimpleTest from "./pages/SimpleTest";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <AuthProvider>
          <NotificationProvider>
          <BrowserRouter>
            <Routes>
              {/* Auth Routes */}
              <Route path="/" element={<Dashboard />} />
              <Route path="/test" element={<SimpleTest />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              
              {/* Routes accessibles sans authentification */}
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/dashboard/calendar" element={<Calendar />} />
              <Route path="/dashboard/profile" element={<Profile />} />
              <Route path="/dashboard/chat" element={<Chat />} />
              <Route path="/dashboard/news" element={<News />} />
              <Route path="/dashboard/resources" element={<Resources />} />
              <Route path="/dashboard/sections" element={<Sections />} />
              <Route path="/dashboard/forum" element={<Forum />} />
              <Route path="/dashboard/groups" element={<Groups />} />
              <Route path="/dashboard/assignments" element={<Assignments />} />
              <Route path="/dashboard/settings" element={<Settings />} />
              <Route path="/dashboard/notifications" element={<Notifications />} />
              <Route path="/dashboard/forum/ask" element={<AskQuestion />} />
              <Route path="/dashboard/forum/question/:questionId" element={<QuestionDetail />} />
              <Route path="/dashboard/study-planner" element={<StudyPlanner />} />
              <Route path="/dashboard/achievements" element={<Achievements />} />
              <Route path="/dashboard/peer-review" element={<PeerReview />} />
              <Route path="/dashboard/performance" element={<Performance />} />
              <Route path="/dashboard/gpa-calculator" element={<GPACalculator />} />
              <Route path="/dashboard/admin/analytics" element={<AdminAnalytics />} />
              <Route path="/dashboard/admin/reports" element={<AdminReports />} />
              <Route path="/dashboard/admin/monitoring" element={<SystemMonitoring />} />
              
              {/* Catch-all for other dashboard routes */}
              <Route 
                path="/dashboard/*" 
                element={
                  <PrivateRoute>
                    <Dashboard />
                  </PrivateRoute>
                } 
              />
              
              {/* Not Found */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
          </NotificationProvider>
        </AuthProvider>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
