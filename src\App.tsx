
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/components/theme/theme-provider";
import PrivateRoute from "@/components/PrivateRoute";

// Pages
import Login from "./pages/Login";
import Register from "./pages/Register";
import ForgotPassword from "./pages/ForgotPassword";
import Dashboard from "./pages/Dashboard";
import Calendar from "./pages/Calendar";
import Profile from "./pages/Profile";
import Chat from "./pages/Chat";
import News from "./pages/News";
import Resources from "./pages/Resources";
import Sections from "./pages/Sections";
import Forum from "./pages/Forum";
import Groups from "./pages/Groups";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <AuthProvider>
          <BrowserRouter>
            <Routes>
              {/* Auth Routes */}
              <Route path="/" element={<Dashboard />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              
              {/* Routes accessibles sans authentification */}
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/dashboard/calendar" element={<Calendar />} />
              <Route path="/dashboard/profile" element={<Profile />} />
              <Route path="/dashboard/chat" element={<Chat />} />
              <Route path="/dashboard/news" element={<News />} />
              <Route path="/dashboard/resources" element={<Resources />} />
              <Route path="/dashboard/sections" element={<Sections />} />
              <Route path="/dashboard/forum" element={<Forum />} />
              <Route path="/dashboard/groups" element={<Groups />} />
              
              {/* Catch-all for other dashboard routes */}
              <Route 
                path="/dashboard/*" 
                element={
                  <PrivateRoute>
                    <Dashboard />
                  </PrivateRoute>
                } 
              />
              
              {/* Not Found */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </AuthProvider>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
