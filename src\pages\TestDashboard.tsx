import DashboardLayout from "@/components/layout/DashboardLayout";

const TestDashboard = () => {
  return (
    <DashboardLayout>
      <div className="p-8">
        <h1 className="text-3xl font-bold mb-4">Test Dashboard</h1>
        <p className="text-lg text-muted-foreground">
          Si vous voyez ce message, le layout fonctionne correctement !
        </p>
        
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-6 bg-card border rounded-lg shadow-sm">
            <h3 className="font-semibold mb-2">Test Card 1</h3>
            <p className="text-sm text-muted-foreground">
              Cette carte teste l'affichage de base.
            </p>
          </div>
          
          <div className="p-6 bg-card border rounded-lg shadow-sm">
            <h3 className="font-semibold mb-2">Test Card 2</h3>
            <p className="text-sm text-muted-foreground">
              Cette carte teste la grille responsive.
            </p>
          </div>
          
          <div className="p-6 bg-card border rounded-lg shadow-sm">
            <h3 className="font-semibold mb-2">Test Card 3</h3>
            <p className="text-sm text-muted-foreground">
              Cette carte teste les styles de base.
            </p>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default TestDashboard;
