import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

// Simple hook for page entrance (placeholder)
export const usePageEntrance = (dependencies: any[]) => {
  // This is a placeholder - in a real app you might use framer-motion or similar
  React.useEffect(() => {
    // Add entrance animations here
  }, dependencies);
};

// Animated Background Component
interface AnimatedBackgroundProps {
  variant?: 'waves' | 'dots' | 'grid';
  className?: string;
}

export const AnimatedBackground: React.FC<AnimatedBackgroundProps> = ({ 
  variant = 'waves', 
  className 
}) => {
  return (
    <div className={cn(
      "fixed inset-0 pointer-events-none",
      variant === 'waves' && "bg-gradient-to-br from-blue-50 to-purple-50",
      variant === 'dots' && "bg-dot-pattern",
      variant === 'grid' && "bg-grid-pattern",
      className
    )} />
  );
};

// Animated Card Component
interface AnimatedCardProps {
  title?: string;
  description?: string;
  children?: React.ReactNode;
  className?: string;
  hoverEffect?: 'lift' | 'glow' | 'scale' | 'tilt';
  entranceAnimation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight';
  delay?: number;
}

export const AnimatedCard: React.FC<AnimatedCardProps> = ({
  title,
  description,
  children,
  className,
  hoverEffect = 'lift',
  entranceAnimation = 'fadeIn',
  delay = 0,
}) => {
  const hoverClasses = {
    lift: 'hover:shadow-lg hover:-translate-y-1',
    glow: 'hover:shadow-xl hover:shadow-blue-200/50',
    scale: 'hover:scale-105',
    tilt: 'hover:rotate-1',
  };

  if (title || description) {
    return (
      <Card className={cn(
        "transition-all duration-300",
        hoverClasses[hoverEffect],
        className
      )}>
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          {children}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "transition-all duration-300",
      hoverClasses[hoverEffect],
      className
    )}>
      <CardContent className="p-6">
        {children}
      </CardContent>
    </Card>
  );
};

// Animated Stats Card Component
interface AnimatedStatsCardProps {
  icon: React.ReactNode;
  title: string;
  value: string | number;
  delay?: number;
  className?: string;
}

export const AnimatedStatsCard: React.FC<AnimatedStatsCardProps> = ({
  icon,
  title,
  value,
  delay = 0,
  className,
}) => {
  return (
    <Card className={cn(
      "transition-all duration-300 hover:shadow-lg hover:-translate-y-1",
      className
    )}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
          </div>
          <div className="text-primary">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Animated Progress Component
interface AnimatedProgressProps {
  value: number;
  className?: string;
  color?: 'default' | 'success' | 'warning' | 'error';
}

export const AnimatedProgress: React.FC<AnimatedProgressProps> = ({
  value,
  className,
  color = 'default',
}) => {
  const colorClasses = {
    default: '',
    success: '[&>div]:bg-green-500',
    warning: '[&>div]:bg-yellow-500',
    error: '[&>div]:bg-red-500',
  };

  return (
    <Progress 
      value={value} 
      className={cn(
        "transition-all duration-500",
        colorClasses[color],
        className
      )} 
    />
  );
};

// Loading Skeleton Components
export const StatsSkeleton = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    {[...Array(4)].map((_, i) => (
      <Card key={i} className="animate-pulse">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-20"></div>
              <div className="h-8 bg-gray-200 rounded w-16"></div>
            </div>
            <div className="h-6 w-6 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    ))}
  </div>
);

export const CardSkeleton = ({ className }: { className?: string }) => (
  <Card className={cn("animate-pulse", className)}>
    <CardHeader>
      <div className="h-6 bg-gray-200 rounded w-32"></div>
      <div className="h-4 bg-gray-200 rounded w-48"></div>
    </CardHeader>
    <CardContent>
      <div className="space-y-3">
        <div className="h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    </CardContent>
  </Card>
);
