# Campus Connect Backend

Backend Django pour l'application Campus Connect - Un portail étudiant moderne avec fonctionnalités Q&A style Stack Overflow.

## 🚀 Fonctionnalités

### 🔐 **Authentification & Utilisateurs**
- Système d'authentification JWT
- Gestion des rôles (Étudiant, Enseignant, Admin)
- Profils utilisateurs complets
- Gestion des sessions
- Système de réputation

### 💬 **Forum Q&A (Style Stack Overflow)**
- Questions et réponses
- Système de votes (upvote/downvote)
- Tags et catégorisation
- Recherche avancée
- Acceptation de réponses
- Commentaires
- Système de réputation
- Badges et gamification

### 📚 **Gestion des Cours**
- Cours et départements
- Inscriptions étudiantes
- Suivi de présence
- Annonces de cours
- Groupes d'étude
- Progression et notes

### 📊 **Analytics & Rapports**
- Statistiques utilisateurs
- Métriques du forum
- Rapports de performance
- Monitoring système

### 🔔 **Notifications**
- Notifications en temps réel
- Préférences utilisateur
- Notifications par email

## 🛠️ Installation Locale

### Prérequis
- Python 3.11+
- PostgreSQL (optionnel, SQLite par défaut)
- Redis (pour Celery)

### 1. Cloner et configurer
```bash
cd campus_connect_backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Configuration
```bash
cp .env.example .env
# Éditer .env avec vos configurations
```

### 3. Base de données
```bash
python manage.py migrate
python scripts/setup.py  # Crée les données initiales
```

### 4. Lancer le serveur
```bash
python manage.py runserver
```

L'API sera disponible sur `http://localhost:8000`

## 🚂 Déploiement sur Railway

### 1. Préparer le projet
```bash
# Assurez-vous que tous les fichiers sont prêts
git add .
git commit -m "Initial backend setup"
```

### 2. Déployer sur Railway
1. Connectez-vous sur [Railway](https://railway.app)
2. Créez un nouveau projet
3. Connectez votre repository GitHub
4. Railway détectera automatiquement Django

### 3. Variables d'environnement Railway
Configurez ces variables dans Railway Dashboard :

```env
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOST=your-app.railway.app
DATABASE_URL=postgresql://... (auto-configuré par Railway)
```

### 4. Base de données PostgreSQL
Railway fournit automatiquement PostgreSQL. La variable `DATABASE_URL` sera configurée automatiquement.

### 5. Commandes post-déploiement
Railway exécutera automatiquement :
```bash
python manage.py migrate
```

## 📡 API Endpoints

### 🔐 **Authentification**
```
POST /api/auth/register/          # Inscription
POST /api/auth/login/             # Connexion
POST /api/auth/logout/            # Déconnexion
GET  /api/auth/profile/           # Profil utilisateur
PUT  /api/auth/profile/           # Modifier profil
POST /api/auth/change-password/   # Changer mot de passe
```

### 💬 **Forum**
```
GET  /api/forum/questions/        # Liste des questions
POST /api/forum/questions/        # Créer question
GET  /api/forum/questions/{id}/   # Détail question
PUT  /api/forum/questions/{id}/   # Modifier question

GET  /api/forum/questions/{id}/answers/  # Réponses
POST /api/forum/questions/{id}/answers/  # Créer réponse
POST /api/forum/answers/{id}/accept/     # Accepter réponse

POST /api/forum/vote/             # Voter
GET  /api/forum/tags/             # Tags
GET  /api/forum/stats/            # Statistiques
```

### 📚 **Cours**
```
GET  /api/courses/                # Liste des cours
POST /api/courses/                # Créer cours
GET  /api/courses/{id}/           # Détail cours
POST /api/courses/{id}/enroll/    # S'inscrire
```

### 📊 **Analytics (Admin)**
```
GET  /api/analytics/users/        # Stats utilisateurs
GET  /api/analytics/forum/        # Stats forum
GET  /api/analytics/courses/      # Stats cours
```

## 📖 Documentation API

Une fois le serveur lancé, accédez à :
- **Swagger UI** : `http://localhost:8000/api/docs/`
- **ReDoc** : `http://localhost:8000/api/redoc/`
- **Schema JSON** : `http://localhost:8000/api/schema/`

## 🔧 Configuration Frontend

Pour connecter le frontend React à ce backend :

### 1. Variables d'environnement Frontend
```env
# .env.local dans le projet React
VITE_API_BASE_URL=http://localhost:8000/api
# ou pour production :
VITE_API_BASE_URL=https://your-app.railway.app/api
```

### 2. Service API Frontend
```typescript
// src/services/api.ts
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const api = {
  // Auth
  login: (credentials) => fetch(`${API_BASE_URL}/auth/login/`, {...}),
  register: (userData) => fetch(`${API_BASE_URL}/auth/register/`, {...}),
  
  // Forum
  getQuestions: () => fetch(`${API_BASE_URL}/forum/questions/`),
  createQuestion: (data) => fetch(`${API_BASE_URL}/forum/questions/`, {...}),
  
  // etc...
};
```

## 🧪 Tests

```bash
# Lancer les tests
python manage.py test

# Tests avec coverage
pip install coverage
coverage run --source='.' manage.py test
coverage report
```

## 📦 Structure du Projet

```
campus_connect_backend/
├── campus_connect/          # Configuration Django
│   ├── settings.py         # Paramètres
│   ├── urls.py            # URLs principales
│   └── wsgi.py            # WSGI
├── accounts/              # Gestion utilisateurs
├── forum/                 # Forum Q&A
├── courses/               # Gestion cours
├── assignments/           # Devoirs
├── achievements/          # Badges/Gamification
├── analytics/             # Statistiques
├── notifications/         # Notifications
├── resources/             # Ressources
├── scripts/               # Scripts utilitaires
├── requirements.txt       # Dépendances
├── Procfile              # Railway deployment
└── railway.json          # Configuration Railway
```

## 🔒 Sécurité

- Authentification JWT avec refresh tokens
- CORS configuré pour le frontend
- Validation des données avec DRF serializers
- Protection CSRF
- Rate limiting (à implémenter)
- Logs de sécurité

## 🚀 Performance

- Optimisation des requêtes avec `select_related` et `prefetch_related`
- Indexation des champs fréquemment recherchés
- Cache Redis pour les sessions
- Pagination automatique
- Compression des réponses

## 📞 Support

### Comptes de Test
- **Admin** : `<EMAIL>` / `admin123`
- **Étudiant** : `<EMAIL>` / `student123`
- **Enseignant** : `<EMAIL>` / `teacher123`

### URLs Utiles
- **API Docs** : `/api/docs/`
- **Admin Panel** : `/admin/`
- **Health Check** : `/health/`

---

**Campus Connect Backend** - Développé avec ❤️ en Django
