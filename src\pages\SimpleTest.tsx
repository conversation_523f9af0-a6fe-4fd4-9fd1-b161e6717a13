const SimpleTest = () => {
  return (
    <div className="min-h-screen bg-background text-foreground p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-6 text-center">
          🚀 Campus Connect - Test Simple
        </h1>
        
        <div className="bg-card border rounded-lg p-6 shadow-sm">
          <h2 className="text-2xl font-semibold mb-4">Test de Base</h2>
          <p className="text-muted-foreground mb-4">
            Si vous voyez cette page, React fonctionne correctement !
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h3 className="font-medium mb-2">✅ React</h3>
              <p className="text-sm text-muted-foreground">Application React fonctionnelle</p>
            </div>
            
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <h3 className="font-medium mb-2">✅ Tailwind CSS</h3>
              <p className="text-sm text-muted-foreground">Styles et mode sombre</p>
            </div>
            
            <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <h3 className="font-medium mb-2">✅ TypeScript</h3>
              <p className="text-sm text-muted-foreground">Typage statique</p>
            </div>
            
            <div className="p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
              <h3 className="font-medium mb-2">✅ Responsive</h3>
              <p className="text-sm text-muted-foreground">Design adaptatif</p>
            </div>
          </div>
          
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <h4 className="font-medium mb-2">🔧 Prochaines étapes :</h4>
            <ol className="text-sm space-y-1 list-decimal list-inside">
              <li>Vérifier que cette page s'affiche correctement</li>
              <li>Tester le mode sombre (F12 → Console → document.documentElement.classList.toggle('dark'))</li>
              <li>Redimensionner la fenêtre pour tester la responsivité</li>
              <li>Si tout fonctionne, revenir au Dashboard complet</li>
            </ol>
          </div>
          
          <div className="mt-6 text-center">
            <button 
              onClick={() => window.location.href = '/dashboard'}
              className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
            >
              Retour au Dashboard
            </button>
          </div>
        </div>
        
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>Campus Connect - Version de test</p>
          <p>Port: {window.location.port} | Host: {window.location.hostname}</p>
        </div>
      </div>
    </div>
  );
};

export default SimpleTest;
