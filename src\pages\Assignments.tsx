import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { 
  FileText, Calendar, Clock, CheckCircle, AlertCircle, 
  Upload, Download, Eye, Plus, Filter, Search 
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";

interface Assignment {
  id: string;
  title: string;
  description: string;
  course: string;
  courseCode: string;
  instructor: string;
  dueDate: Date;
  maxPoints: number;
  submissionType: "file" | "text" | "both";
  status: "pending" | "submitted" | "graded" | "overdue";
  submittedAt?: Date;
  grade?: number;
  feedback?: string;
  attachments?: string[];
}

const Assignments = () => {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTab, setSelectedTab] = useState("all");

  // Mock assignments data
  const [assignments] = useState<Assignment[]>([
    {
      id: "1",
      title: "Final Project: Web Application Development",
      description: "Create a full-stack web application using React and Node.js. The application should include user authentication, CRUD operations, and a responsive design.",
      course: "Introduction to Computer Science",
      courseCode: "CS 101",
      instructor: "Dr. Sarah Miller",
      dueDate: new Date("2025-05-25T23:59:00"),
      maxPoints: 100,
      submissionType: "both",
      status: "pending",
    },
    {
      id: "2",
      title: "Calculus Problem Set #8",
      description: "Complete problems 1-15 from Chapter 8. Show all work and provide detailed explanations for each solution.",
      course: "Calculus II",
      courseCode: "MATH 242",
      instructor: "Prof. Robert Chen",
      dueDate: new Date("2025-05-20T17:00:00"),
      maxPoints: 50,
      submissionType: "file",
      status: "submitted",
      submittedAt: new Date("2025-05-19T14:30:00"),
    },
    {
      id: "3",
      title: "Technical Writing Portfolio",
      description: "Compile a portfolio of 5 technical writing samples demonstrating different styles and formats.",
      course: "Technical Writing",
      courseCode: "ENG 210",
      instructor: "Dr. James Wilson",
      dueDate: new Date("2025-05-18T23:59:00"),
      maxPoints: 75,
      submissionType: "file",
      status: "graded",
      submittedAt: new Date("2025-05-17T20:15:00"),
      grade: 68,
      feedback: "Good work overall. The technical documentation was well-structured, but the user manual could use more detailed explanations.",
    },
    {
      id: "4",
      title: "Algorithm Analysis Report",
      description: "Analyze the time and space complexity of three sorting algorithms and provide a comparative study.",
      course: "Data Structures and Algorithms",
      courseCode: "CS 201",
      instructor: "Dr. Fatima Aliyev",
      dueDate: new Date("2025-05-15T23:59:00"),
      maxPoints: 80,
      submissionType: "both",
      status: "overdue",
    },
  ]);

  const getFilteredAssignments = () => {
    let filtered = assignments;

    // Filter by tab
    switch (selectedTab) {
      case "pending":
        filtered = filtered.filter(a => a.status === "pending");
        break;
      case "submitted":
        filtered = filtered.filter(a => a.status === "submitted");
        break;
      case "graded":
        filtered = filtered.filter(a => a.status === "graded");
        break;
      case "overdue":
        filtered = filtered.filter(a => a.status === "overdue");
        break;
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(a => 
        a.title.toLowerCase().includes(query) ||
        a.course.toLowerCase().includes(query) ||
        a.courseCode.toLowerCase().includes(query)
      );
    }

    return filtered;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Pending</Badge>;
      case "submitted":
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Submitted</Badge>;
      case "graded":
        return <Badge variant="outline" className="text-green-600 border-green-600">Graded</Badge>;
      case "overdue":
        return <Badge variant="destructive">Overdue</Badge>;
      default:
        return null;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case "submitted":
        return <Upload className="h-4 w-4 text-blue-600" />;
      case "graded":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "overdue":
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const formatDueDate = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 0) {
      return `Overdue by ${Math.abs(Math.floor(diffInHours / 24))} days`;
    } else if (diffInHours < 24) {
      return `Due in ${diffInHours} hours`;
    } else {
      const days = Math.floor(diffInHours / 24);
      return `Due in ${days} day${days !== 1 ? 's' : ''}`;
    }
  };

  const handleSubmitAssignment = (assignmentId: string) => {
    toast.info("Assignment submission feature coming soon!");
  };

  const handleViewSubmission = (assignmentId: string) => {
    toast.info("Viewing submission details...");
  };

  const isTeacher = user?.role === "teacher";

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-6xl">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Assignments</h1>
            <p className="text-muted-foreground">
              {isTeacher ? "Manage and grade student assignments" : "View and submit your assignments"}
            </p>
          </div>
          {isTeacher && (
            <Button className="mt-4 md:mt-0">
              <Plus className="mr-2 h-4 w-4" />
              Create Assignment
            </Button>
          )}
        </div>

        <div className="bg-card rounded-lg shadow border">
          <div className="p-4 border-b">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search assignments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>

          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
            <div className="px-4 border-b">
              <TabsList className="bg-transparent border-b-0 justify-start">
                <TabsTrigger value="all">All Assignments</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="submitted">Submitted</TabsTrigger>
                <TabsTrigger value="graded">Graded</TabsTrigger>
                <TabsTrigger value="overdue">Overdue</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value={selectedTab} className="m-0">
              <div className="p-4 space-y-4">
                {getFilteredAssignments().length > 0 ? (
                  getFilteredAssignments().map((assignment) => (
                    <AssignmentCard 
                      key={assignment.id} 
                      assignment={assignment}
                      onSubmit={handleSubmitAssignment}
                      onView={handleViewSubmission}
                      isTeacher={isTeacher}
                    />
                  ))
                ) : (
                  <div className="text-center py-12">
                    <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No assignments found</h3>
                    <p className="text-muted-foreground">
                      {searchQuery 
                        ? "Try adjusting your search query" 
                        : selectedTab === "pending"
                          ? "No pending assignments"
                          : `No ${selectedTab} assignments`
                      }
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </DashboardLayout>
  );
};

interface AssignmentCardProps {
  assignment: Assignment;
  onSubmit: (id: string) => void;
  onView: (id: string) => void;
  isTeacher: boolean;
}

const AssignmentCard = ({ assignment, onSubmit, onView, isTeacher }: AssignmentCardProps) => {
  const getGradeColor = (grade: number, maxPoints: number) => {
    const percentage = (grade / maxPoints) * 100;
    if (percentage >= 90) return "text-green-600";
    if (percentage >= 80) return "text-blue-600";
    if (percentage >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary">{assignment.courseCode}</Badge>
              {getStatusBadge(assignment.status)}
            </div>
            <CardTitle className="text-lg">{assignment.title}</CardTitle>
            <CardDescription className="mt-1">
              {assignment.course} • {assignment.instructor}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon(assignment.status)}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
          {assignment.description}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Due Date</p>
              <p className="text-xs text-muted-foreground">
                {assignment.dueDate.toLocaleDateString()} at {assignment.dueDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </p>
              <p className="text-xs text-orange-600">
                {formatDueDate(assignment.dueDate)}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Points</p>
              <p className="text-xs text-muted-foreground">
                {assignment.grade !== undefined 
                  ? `${assignment.grade}/${assignment.maxPoints}` 
                  : `${assignment.maxPoints} points`
                }
              </p>
              {assignment.grade !== undefined && (
                <p className={`text-xs font-medium ${getGradeColor(assignment.grade, assignment.maxPoints)}`}>
                  {Math.round((assignment.grade / assignment.maxPoints) * 100)}%
                </p>
              )}
            </div>
          </div>

          {assignment.submittedAt && (
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">Submitted</p>
                <p className="text-xs text-muted-foreground">
                  {assignment.submittedAt.toLocaleDateString()}
                </p>
              </div>
            </div>
          )}
        </div>

        {assignment.feedback && (
          <div className="bg-muted/50 p-3 rounded-md mb-4">
            <p className="text-sm font-medium mb-1">Instructor Feedback:</p>
            <p className="text-sm text-muted-foreground">{assignment.feedback}</p>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {assignment.status === "pending" && (
              <Button size="sm" onClick={() => onSubmit(assignment.id)}>
                <Upload className="mr-2 h-4 w-4" />
                Submit Assignment
              </Button>
            )}
            {(assignment.status === "submitted" || assignment.status === "graded") && (
              <Button variant="outline" size="sm" onClick={() => onView(assignment.id)}>
                <Eye className="mr-2 h-4 w-4" />
                View Submission
              </Button>
            )}
          </div>

          {isTeacher && assignment.status === "submitted" && (
            <Button variant="outline" size="sm">
              Grade Assignment
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default Assignments;
