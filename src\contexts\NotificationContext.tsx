import { createContext, useContext, useState, ReactNode, useEffect } from "react";
import { toast } from "sonner";

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  category: "assignment" | "grade" | "announcement" | "forum" | "chat" | "system";
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, "id" | "timestamp" | "read">) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider = ({ children }: { children: ReactNode }) => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: "1",
      title: "New Assignment Posted",
      message: "CS 101: Final Project has been posted. Due date: May 25th",
      type: "info",
      category: "assignment",
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      read: false,
      actionUrl: "/dashboard/assignments",
      actionText: "View Assignment"
    },
    {
      id: "2",
      title: "Grade Posted",
      message: "Your grade for Technical Writing Portfolio has been posted: 68/75",
      type: "success",
      category: "grade",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      read: false,
      actionUrl: "/dashboard/assignments",
      actionText: "View Grade"
    },
    {
      id: "3",
      title: "Forum Reply",
      message: "Someone replied to your question about Java programming",
      type: "info",
      category: "forum",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
      read: true,
      actionUrl: "/dashboard/forum",
      actionText: "View Reply"
    },
    {
      id: "4",
      title: "System Maintenance",
      message: "Scheduled maintenance tonight from 10 PM to 2 AM",
      type: "warning",
      category: "system",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      read: false,
      actionUrl: "/dashboard/news",
      actionText: "Read More"
    },
    {
      id: "5",
      title: "New Chat Message",
      message: "You have a new message in CS Project Team group",
      type: "info",
      category: "chat",
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 8), // 8 hours ago
      read: true,
      actionUrl: "/dashboard/chat",
      actionText: "Open Chat"
    }
  ]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const addNotification = (notificationData: Omit<Notification, "id" | "timestamp" | "read">) => {
    const newNotification: Notification = {
      ...notificationData,
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false,
    };

    setNotifications(prev => [newNotification, ...prev]);

    // Show toast notification
    toast(notificationData.title, {
      description: notificationData.message,
      action: notificationData.actionUrl ? {
        label: notificationData.actionText || "View",
        onClick: () => window.location.href = notificationData.actionUrl!,
      } : undefined,
    });
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  // Simulate real-time notifications
  useEffect(() => {
    const interval = setInterval(() => {
      // Randomly add new notifications for demo purposes
      const randomNotifications = [
        {
          title: "Assignment Reminder",
          message: "Don't forget: Calculus Problem Set #8 is due tomorrow",
          type: "warning" as const,
          category: "assignment" as const,
        },
        {
          title: "New Announcement",
          message: "Library hours extended during finals week",
          type: "info" as const,
          category: "announcement" as const,
          actionUrl: "/dashboard/news",
          actionText: "Read More"
        },
        {
          title: "Forum Activity",
          message: "New question posted in Computer Science forum",
          type: "info" as const,
          category: "forum" as const,
          actionUrl: "/dashboard/forum",
          actionText: "View Question"
        }
      ];

      // 10% chance to add a notification every 30 seconds
      if (Math.random() < 0.1) {
        const randomNotification = randomNotifications[Math.floor(Math.random() * randomNotifications.length)];
        addNotification(randomNotification);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        removeNotification,
        clearAllNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationProvider");
  }
  return context;
};
