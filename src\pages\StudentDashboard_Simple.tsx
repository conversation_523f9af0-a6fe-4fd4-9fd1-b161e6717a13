import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  BookOpen, 
  Calendar, 
  Trophy, 
  Target,
  Clock,
  Star,
  TrendingUp,
  Users,
  MessageSquare,
  FileText,
  Award
} from "lucide-react";

const StudentDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  // Mock data
  const stats = {
    gpa: 3.7,
    hoursStudied: 42,
    streak: 7,
    completedAssignments: 15,
    totalAssignments: 18
  };

  const upcomingAssignments = [
    {
      id: 1,
      title: "Calculus II - Integration Project",
      course: "MATH 242",
      dueDate: "2025-01-18",
      priority: "high",
      progress: 60
    },
    {
      id: 2,
      title: "Data Structures Lab Report",
      course: "CS 201",
      dueDate: "2025-01-20",
      priority: "medium",
      progress: 30
    },
    {
      id: 3,
      title: "Technical Writing Essay",
      course: "ENG 210",
      dueDate: "2025-01-22",
      priority: "low",
      progress: 80
    }
  ];

  const todaySchedule = [
    { time: "09:00", course: "CS 101", room: "Tech 305", type: "lecture" },
    { time: "11:00", course: "MATH 242", room: "Science 120", type: "tutorial" },
    { time: "14:00", course: "ENG 210", room: "Arts 210", type: "workshop" }
  ];

  const recentAchievements = [
    { title: "Week Warrior", description: "7 days study streak", icon: "🔥" },
    { title: "Quiz Master", description: "Perfect score on CS quiz", icon: "🎯" },
    { title: "Early Bird", description: "Submitted assignment early", icon: "⏰" }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200";
      case "medium": return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-200";
      case "low": return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200";
      default: return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-200";
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Bonjour, {user?.firstName} ! 👋
            </h1>
            <p className="text-muted-foreground mt-1">
              Prêt pour une nouvelle journée d'apprentissage ?
            </p>
          </div>
          <div className="mt-4 md:mt-0 text-right">
            <p className="text-sm text-muted-foreground">Aujourd'hui</p>
            <p className="text-lg font-semibold text-foreground">
              {new Date().toLocaleDateString('fr-FR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">GPA Actuel</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stats.gpa}</div>
              <p className="text-xs text-muted-foreground">+0.2 ce semestre</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Heures d'Étude</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stats.hoursStudied}h</div>
              <p className="text-xs text-muted-foreground">Cette semaine</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Streak</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">{stats.streak} jours</div>
              <p className="text-xs text-muted-foreground">Continue comme ça ! 🔥</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Devoirs</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">
                {stats.completedAssignments}/{stats.totalAssignments}
              </div>
              <p className="text-xs text-muted-foreground">Complétés</p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Actions Rapides
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => navigate('/dashboard/study-planner')}
              >
                <BookOpen className="h-6 w-6" />
                <span className="text-sm">Session d'Étude</span>
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => navigate('/dashboard/assignments')}
              >
                <FileText className="h-6 w-6" />
                <span className="text-sm">Mes Notes</span>
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => navigate('/dashboard/forum')}
              >
                <MessageSquare className="h-6 w-6" />
                <span className="text-sm">Forum Q&A</span>
              </Button>
              <Button 
                variant="outline" 
                className="h-20 flex flex-col gap-2"
                onClick={() => navigate('/dashboard/achievements')}
              >
                <Award className="h-6 w-6" />
                <span className="text-sm">Achievements</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Upcoming Assignments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Devoirs à Venir
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {upcomingAssignments.map((assignment) => (
                <div key={assignment.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium text-foreground">{assignment.title}</h4>
                    <Badge className={getPriorityColor(assignment.priority)}>
                      {assignment.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{assignment.course}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      Due: {new Date(assignment.dueDate).toLocaleDateString('fr-FR')}
                    </span>
                    <div className="flex items-center gap-2">
                      <Progress value={assignment.progress} className="w-20" />
                      <span className="text-sm text-muted-foreground">{assignment.progress}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Today's Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Planning du Jour
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {todaySchedule.map((item, index) => (
                <div key={index} className="flex items-center gap-4 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="text-center">
                    <div className="font-semibold text-foreground">{item.time}</div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-foreground">{item.course}</h4>
                    <p className="text-sm text-muted-foreground">{item.room} • {item.type}</p>
                  </div>
                  <Badge variant="outline">{item.type}</Badge>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Recent Achievements */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Succès Récents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {recentAchievements.map((achievement, index) => (
                <div key={index} className="flex items-center gap-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div>
                    <h4 className="font-medium text-foreground">{achievement.title}</h4>
                    <p className="text-sm text-muted-foreground">{achievement.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Motivational Quote */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-foreground mb-2">💡 Citation du Jour</h3>
            <p className="text-muted-foreground italic">
              "Le succès, c'est d'aller d'échec en échec sans perdre son enthousiasme."
            </p>
            <p className="text-sm text-muted-foreground mt-2">- Winston Churchill</p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default StudentDashboard;
