import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import StudentDashboard from "./StudentDashboard_Simple";
import AdminDashboard from "./AdminDashboard_Simple";
import TeacherDashboard from "./TeacherDashboard_Simple";

const Dashboard = () => {
  const { user } = useAuth();
  
  console.log("Dashboard rendering, user:", user);

  // Render specialized dashboards based on user role with error handling
  try {
    if (!user || user.role === "student") {
      return <StudentDashboard />;
    }

    if (user.role === "admin") {
      return <AdminDashboard />;
    }

    if (user.role === "teacher") {
      return <TeacherDashboard />;
    }

    // Fallback to student dashboard
    return <StudentDashboard />;
  } catch (error) {
    console.error("Dashboard error:", error);
    
    // Fallback dashboard if there's an error
    return (
      <DashboardLayout>
        <div className="p-8">
          <div className="bg-card border rounded-lg p-6 shadow-sm">
            <h1 className="text-3xl font-bold mb-4 text-foreground">
              🎓 Campus Connect Dashboard - Erreur
            </h1>
            <p className="text-muted-foreground mb-6">
              Une erreur s'est produite lors du chargement du dashboard. Voici une interface de secours.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                  🎓 Dashboard Étudiant
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Suivi des cours, notes et progression académique
                </p>
              </div>
              
              <div className="p-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h3 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                  👨‍🏫 Dashboard Enseignant
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300">
                  Gestion des cours et suivi des étudiants
                </p>
              </div>
              
              <div className="p-6 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                <h3 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">
                  🛡️ Dashboard Admin
                </h3>
                <p className="text-sm text-purple-700 dark:text-purple-300">
                  Analytics, rapports et monitoring système
                </p>
              </div>
            </div>
            
            <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
                🔧 Informations de Debug
              </h4>
              <ul className="text-sm text-yellow-800 dark:text-yellow-200 space-y-1">
                <li>• Utilisateur connecté : {user?.firstName} {user?.lastName}</li>
                <li>• Email : {user?.email}</li>
                <li>• Rôle : {user?.role}</li>
                <li>• Erreur dans le dashboard spécialisé</li>
                <li>• Interface de secours activée ✅</li>
              </ul>
            </div>
            
            <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <h4 className="font-medium text-red-900 dark:text-red-100 mb-2">
                🚨 Mode de Secours Activé
              </h4>
              <ul className="text-sm text-red-800 dark:text-red-200 space-y-1">
                <li>• Une erreur s'est produite dans le dashboard spécialisé</li>
                <li>• Cette interface de secours garantit que l'application reste fonctionnelle</li>
                <li>• Utilisez le RoleSwitcher pour changer de rôle</li>
                <li>• Naviguez vers les autres pages qui fonctionnent normalement</li>
              </ul>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }
};

export default Dashboard;
