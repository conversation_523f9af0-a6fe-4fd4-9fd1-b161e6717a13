import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { 
  Users, 
  Star, 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Eye,
  Edit,
  Send,
  Download,
  MessageSquare,
  ThumbsUp,
  ThumbsDown
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { toast } from "sonner";

interface PeerReviewAssignment {
  id: string;
  title: string;
  description: string;
  course: string;
  dueDate: Date;
  maxReviews: number;
  submissionDeadline: Date;
  reviewDeadline: Date;
  status: "open" | "reviewing" | "completed";
  mySubmission?: {
    id: string;
    title: string;
    submittedAt: Date;
    fileUrl: string;
  };
  assignedReviews: PeerReview[];
  receivedReviews: PeerReview[];
}

interface PeerReview {
  id: string;
  reviewerId: string;
  reviewerName: string;
  submissionId: string;
  submissionTitle: string;
  authorName: string;
  criteria: ReviewCriteria[];
  overallRating: number;
  comments: string;
  submittedAt?: Date;
  status: "pending" | "completed";
}

interface ReviewCriteria {
  id: string;
  name: string;
  description: string;
  rating: number;
  maxRating: number;
  comments?: string;
}

const PeerReview = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("assignments");
  const [selectedReview, setSelectedReview] = useState<PeerReview | null>(null);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);

  // Mock data
  const [assignments] = useState<PeerReviewAssignment[]>([
    {
      id: "1",
      title: "Web Development Project Peer Review",
      description: "Review your peers' web development projects focusing on code quality, design, and functionality.",
      course: "CS 101 - Introduction to Computer Science",
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      maxReviews: 3,
      submissionDeadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      reviewDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      status: "reviewing",
      mySubmission: {
        id: "sub1",
        title: "E-commerce Website",
        submittedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        fileUrl: "/project.zip"
      },
      assignedReviews: [
        {
          id: "rev1",
          reviewerId: user?.id || "current-user",
          reviewerName: `${user?.firstName} ${user?.lastName}`,
          submissionId: "sub2",
          submissionTitle: "Social Media Dashboard",
          authorName: "Alice Johnson",
          status: "pending",
          overallRating: 0,
          comments: "",
          criteria: [
            {
              id: "c1",
              name: "Code Quality",
              description: "Clean, readable, and well-structured code",
              rating: 0,
              maxRating: 5
            },
            {
              id: "c2",
              name: "User Interface",
              description: "Intuitive and visually appealing design",
              rating: 0,
              maxRating: 5
            },
            {
              id: "c3",
              name: "Functionality",
              description: "All features work as expected",
              rating: 0,
              maxRating: 5
            },
            {
              id: "c4",
              name: "Innovation",
              description: "Creative solutions and unique features",
              rating: 0,
              maxRating: 5
            }
          ]
        },
        {
          id: "rev2",
          reviewerId: user?.id || "current-user",
          reviewerName: `${user?.firstName} ${user?.lastName}`,
          submissionId: "sub3",
          submissionTitle: "Task Management App",
          authorName: "Bob Smith",
          status: "completed",
          overallRating: 4.2,
          comments: "Great project with excellent functionality. The UI could be improved but overall very solid work.",
          submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          criteria: [
            {
              id: "c1",
              name: "Code Quality",
              description: "Clean, readable, and well-structured code",
              rating: 4,
              maxRating: 5,
              comments: "Well-organized code with good commenting"
            },
            {
              id: "c2",
              name: "User Interface",
              description: "Intuitive and visually appealing design",
              rating: 3,
              maxRating: 5,
              comments: "Functional but could be more polished"
            },
            {
              id: "c3",
              name: "Functionality",
              description: "All features work as expected",
              rating: 5,
              maxRating: 5,
              comments: "All features work perfectly"
            },
            {
              id: "c4",
              name: "Innovation",
              description: "Creative solutions and unique features",
              rating: 4,
              maxRating: 5,
              comments: "Some creative features implemented"
            }
          ]
        }
      ],
      receivedReviews: [
        {
          id: "rec1",
          reviewerId: "reviewer1",
          reviewerName: "Carol Davis",
          submissionId: "sub1",
          submissionTitle: "E-commerce Website",
          authorName: `${user?.firstName} ${user?.lastName}`,
          status: "completed",
          overallRating: 4.5,
          comments: "Excellent work! The e-commerce functionality is well implemented and the design is professional.",
          submittedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          criteria: [
            {
              id: "c1",
              name: "Code Quality",
              description: "Clean, readable, and well-structured code",
              rating: 5,
              maxRating: 5,
              comments: "Very clean and well-documented code"
            },
            {
              id: "c2",
              name: "User Interface",
              description: "Intuitive and visually appealing design",
              rating: 4,
              maxRating: 5,
              comments: "Professional design, minor improvements possible"
            },
            {
              id: "c3",
              name: "Functionality",
              description: "All features work as expected",
              rating: 5,
              maxRating: 5,
              comments: "All e-commerce features work flawlessly"
            },
            {
              id: "c4",
              name: "Innovation",
              description: "Creative solutions and unique features",
              rating: 4,
              maxRating: 5,
              comments: "Good use of modern web technologies"
            }
          ]
        }
      ]
    }
  ]);

  const [currentReview, setCurrentReview] = useState<PeerReview | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open": return "text-blue-600 bg-blue-50 border-blue-200";
      case "reviewing": return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "completed": return "text-green-600 bg-green-50 border-green-200";
      default: return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const formatTimeRemaining = (date: Date) => {
    const now = new Date();
    const diff = date.getTime() - now.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    
    if (days > 0) return `${days} day${days !== 1 ? 's' : ''} left`;
    if (hours > 0) return `${hours} hour${hours !== 1 ? 's' : ''} left`;
    return "Due soon";
  };

  const startReview = (review: PeerReview) => {
    setCurrentReview(review);
    setIsReviewDialogOpen(true);
  };

  const submitReview = () => {
    if (!currentReview) return;
    
    // In a real app, submit the review to the backend
    toast.success("Review submitted successfully!");
    setIsReviewDialogOpen(false);
    setCurrentReview(null);
  };

  const updateCriteriaRating = (criteriaId: string, rating: number) => {
    if (!currentReview) return;
    
    setCurrentReview(prev => ({
      ...prev!,
      criteria: prev!.criteria.map(c => 
        c.id === criteriaId ? { ...c, rating } : c
      )
    }));
  };

  const updateCriteriaComments = (criteriaId: string, comments: string) => {
    if (!currentReview) return;
    
    setCurrentReview(prev => ({
      ...prev!,
      criteria: prev!.criteria.map(c => 
        c.id === criteriaId ? { ...c, comments } : c
      )
    }));
  };

  const getAverageRating = () => {
    if (!currentReview) return 0;
    const total = currentReview.criteria.reduce((sum, c) => sum + c.rating, 0);
    return total / currentReview.criteria.length;
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto max-w-6xl">
        <div className="mb-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6" />
            Peer Review
          </h1>
          <p className="text-muted-foreground">
            Review your peers' work and receive feedback on your submissions
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="assignments">Assignments</TabsTrigger>
            <TabsTrigger value="to-review">To Review ({assignments[0]?.assignedReviews.filter(r => r.status === "pending").length || 0})</TabsTrigger>
            <TabsTrigger value="my-reviews">My Reviews</TabsTrigger>
          </TabsList>

          {/* Assignments Tab */}
          <TabsContent value="assignments" className="mt-6">
            <div className="space-y-6">
              {assignments.map((assignment) => (
                <Card key={assignment.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle>{assignment.title}</CardTitle>
                        <CardDescription className="mt-1">
                          {assignment.course}
                        </CardDescription>
                      </div>
                      <Badge className={getStatusColor(assignment.status)}>
                        {assignment.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground mb-4">
                      {assignment.description}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Submission Deadline</p>
                          <p className="text-xs text-muted-foreground">
                            {assignment.submissionDeadline.toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Reviews Required</p>
                          <p className="text-xs text-muted-foreground">
                            {assignment.maxReviews} per student
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Review Deadline</p>
                          <p className="text-xs text-muted-foreground">
                            {formatTimeRemaining(assignment.reviewDeadline)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {assignment.mySubmission && (
                      <div className="bg-muted/50 p-4 rounded-lg mb-4">
                        <h4 className="font-medium mb-2">My Submission</h4>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium">{assignment.mySubmission.title}</p>
                            <p className="text-xs text-muted-foreground">
                              Submitted {assignment.mySubmission.submittedAt.toLocaleDateString()}
                            </p>
                          </div>
                          <Button variant="outline" size="sm">
                            <Eye className="mr-2 h-4 w-4" />
                            View
                          </Button>
                        </div>
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">Reviews to Complete</h4>
                        <div className="space-y-2">
                          {assignment.assignedReviews.map((review) => (
                            <div key={review.id} className="flex items-center justify-between p-2 border rounded">
                              <div>
                                <p className="text-sm font-medium">{review.submissionTitle}</p>
                                <p className="text-xs text-muted-foreground">by {review.authorName}</p>
                              </div>
                              <div className="flex items-center gap-2">
                                {review.status === "completed" ? (
                                  <Badge variant="outline" className="text-green-600 border-green-600">
                                    <CheckCircle className="mr-1 h-3 w-3" />
                                    Completed
                                  </Badge>
                                ) : (
                                  <Button size="sm" onClick={() => startReview(review)}>
                                    Start Review
                                  </Button>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium mb-2">Reviews Received</h4>
                        <div className="space-y-2">
                          {assignment.receivedReviews.map((review) => (
                            <div key={review.id} className="p-2 border rounded">
                              <div className="flex items-center justify-between mb-2">
                                <p className="text-sm font-medium">by {review.reviewerName}</p>
                                <div className="flex items-center gap-1">
                                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                  <span className="text-sm font-medium">{review.overallRating.toFixed(1)}</span>
                                </div>
                              </div>
                              <p className="text-xs text-muted-foreground">{review.comments}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* To Review Tab */}
          <TabsContent value="to-review" className="mt-6">
            <div className="space-y-4">
              {assignments[0]?.assignedReviews.filter(r => r.status === "pending").map((review) => (
                <Card key={review.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg">{review.submissionTitle}</h3>
                        <p className="text-muted-foreground">by {review.authorName}</p>
                        <p className="text-sm text-muted-foreground mt-2">
                          Part of: Web Development Project Peer Review
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="mr-2 h-4 w-4" />
                          Download
                        </Button>
                        <Button onClick={() => startReview(review)}>
                          Start Review
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* My Reviews Tab */}
          <TabsContent value="my-reviews" className="mt-6">
            <div className="space-y-4">
              {assignments[0]?.assignedReviews.filter(r => r.status === "completed").map((review) => (
                <Card key={review.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="font-semibold">{review.submissionTitle}</h3>
                        <p className="text-muted-foreground">by {review.authorName}</p>
                        <p className="text-sm text-muted-foreground">
                          Reviewed {review.submittedAt?.toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="h-5 w-5 text-yellow-500 fill-current" />
                        <span className="font-medium">{review.overallRating.toFixed(1)}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      {review.criteria.map((criteria) => (
                        <div key={criteria.id} className="flex items-center justify-between">
                          <span className="text-sm">{criteria.name}</span>
                          <div className="flex items-center gap-1">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < criteria.rating ? "text-yellow-500 fill-current" : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                    
                    <Separator className="my-4" />
                    <p className="text-sm">{review.comments}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Review Dialog */}
        <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Review: {currentReview?.submissionTitle}</DialogTitle>
              <DialogDescription>
                by {currentReview?.authorName}
              </DialogDescription>
            </DialogHeader>
            
            {currentReview && (
              <div className="space-y-6">
                {/* Criteria Ratings */}
                <div className="space-y-4">
                  <h3 className="font-semibold">Evaluation Criteria</h3>
                  {currentReview.criteria.map((criteria) => (
                    <div key={criteria.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{criteria.name}</h4>
                          <p className="text-sm text-muted-foreground">{criteria.description}</p>
                        </div>
                        <div className="flex items-center gap-1">
                          {[...Array(criteria.maxRating)].map((_, i) => (
                            <Button
                              key={i}
                              variant="ghost"
                              size="sm"
                              onClick={() => updateCriteriaRating(criteria.id, i + 1)}
                              className="p-1"
                            >
                              <Star
                                className={`h-5 w-5 ${
                                  i < criteria.rating ? "text-yellow-500 fill-current" : "text-gray-300"
                                }`}
                              />
                            </Button>
                          ))}
                        </div>
                      </div>
                      <Textarea
                        placeholder="Comments for this criteria..."
                        value={criteria.comments || ""}
                        onChange={(e) => updateCriteriaComments(criteria.id, e.target.value)}
                        className="text-sm"
                      />
                    </div>
                  ))}
                </div>

                {/* Overall Rating */}
                <div className="bg-muted/50 p-4 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Overall Rating</span>
                    <div className="flex items-center gap-2">
                      <span className="text-2xl font-bold">{getAverageRating().toFixed(1)}</span>
                      <Star className="h-6 w-6 text-yellow-500 fill-current" />
                    </div>
                  </div>
                </div>

                {/* Overall Comments */}
                <div className="space-y-2">
                  <Label htmlFor="overall-comments">Overall Comments</Label>
                  <Textarea
                    id="overall-comments"
                    placeholder="Provide overall feedback on the submission..."
                    value={currentReview.comments}
                    onChange={(e) => setCurrentReview(prev => ({ ...prev!, comments: e.target.value }))}
                  />
                </div>

                {/* Submit Button */}
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsReviewDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={submitReview}>
                    <Send className="mr-2 h-4 w-4" />
                    Submit Review
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
};

export default PeerReview;
