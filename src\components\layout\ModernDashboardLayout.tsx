import { ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import {
  CalendarCheck, FileText, Home, LogOut, MessageSquare,
  Settings, UserRound, Users, Book, Bell
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ThemeSwitcher } from "@/components/theme/ThemeSwitcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger
} from "@/components/ui/sidebar";

interface ModernDashboardLayoutProps {
  children: ReactNode;
}

export function ModernDashboardLayout({ children }: ModernDashboardLayoutProps) {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  
  // Utilisateur par défaut si non connecté
  const defaultUser = {
    firstName: "Guest",
    lastName: "User",
    role: "student",
    profilePicture: ""
  };
  
  const currentUser = user || defaultUser;
  
  const handleLogout = () => {
    if (user) logout();
    navigate("/login");
  };
  
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <SidebarProvider>
      <div className="flex h-screen">
        <Sidebar>
          <SidebarHeader className="flex items-center px-4 py-2">
            <h1 className="text-xl font-bold">Campus Connect</h1>
          </SidebarHeader>
          <SidebarContent>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton 
                  onClick={() => navigate("/dashboard")}
                  isActive={window.location.pathname === "/dashboard"}
                  tooltip="Dashboard"
                >
                  <Home className="mr-2 h-4 w-4" />
                  <span>Dashboard</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton 
                  onClick={() => navigate("/dashboard/sections")}
                  isActive={window.location.pathname === "/dashboard/sections"}
                  tooltip="Sections & Courses"
                >
                  <Book className="mr-2 h-4 w-4" />
                  <span>Sections & Courses</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
              {/* Ajouter d'autres éléments de menu ici */}
            </SidebarMenu>
          </SidebarContent>
          <SidebarFooter className="p-4">
            <div className="flex items-center mb-4">
              <Avatar>
                <AvatarImage src={currentUser?.profilePicture} />
                <AvatarFallback>
                  {getInitials(`${currentUser.firstName} ${currentUser.lastName}`)}
                </AvatarFallback>
              </Avatar>
              <div className="ml-3">
                <p className="text-sm font-semibold">
                  {currentUser.firstName} {currentUser.lastName}
                </p>
                <p className="text-xs text-muted-foreground capitalize">
                  {currentUser.role}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={handleLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              {user ? "Logout" : "Login"}
            </Button>
          </SidebarFooter>
        </Sidebar>
        <div className="flex-1 overflow-auto">
          <header className="bg-card shadow-sm z-10 p-4 flex justify-end items-center">
            <div className="flex items-center space-x-4">
              <ThemeSwitcher />
              <SidebarTrigger className="md:hidden" />
            </div>
          </header>
          <main className="p-6">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}