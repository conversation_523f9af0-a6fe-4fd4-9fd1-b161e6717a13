# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOST=your-domain.railway.app

# Database (Railway PostgreSQL)
DATABASE_URL=postgresql://username:password@host:port/database

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Redis (for Celery)
REDIS_URL=redis://localhost:6379

# Django Log Level
DJANGO_LOG_LEVEL=INFO

# File Storage (Optional - for production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1
