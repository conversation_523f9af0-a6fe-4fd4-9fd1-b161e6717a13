import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  GraduationCap, 
  Shield, 
  Settings,
  User,
  Crown,
  BookOpen
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const RoleSwitcher = () => {
  const { user, loginAsUser } = useAuth();
  const [isOpen, setIsOpen] = useState(false);

  const roles = [
    {
      id: "student",
      name: "Étudian<PERSON>",
      description: "Accès aux cours, devoirs et outils d'apprentissage",
      icon: <BookOpen className="h-6 w-6" />,
      color: "bg-blue-500",
      features: [
        "Dashboard personnalisé",
        "Suivi des cours et notes",
        "Planificateur d'études",
        "Forum et peer review",
        "Système de badges",
        "Calculatrice GPA"
      ]
    },
    {
      id: "teacher",
      name: "Enseignant",
      description: "Gestion des cours, étudiants et évaluations",
      icon: <GraduationCap className="h-6 w-6" />,
      color: "bg-green-500",
      features: [
        "Gestion des cours",
        "Notation et évaluations",
        "Suivi des étudiants",
        "Analytics de performance",
        "Forum Q&A",
        "Planning des cours"
      ]
    },
    {
      id: "admin",
      name: "Administrateur",
      description: "Gestion complète de la plateforme et des utilisateurs",
      icon: <Shield className="h-6 w-6" />,
      color: "bg-purple-500",
      features: [
        "Gestion des utilisateurs",
        "Statistiques globales",
        "Configuration système",
        "Rapports et analytics",
        "Approbations",
        "Monitoring système"
      ]
    }
  ];

  const switchRole = (roleId: string) => {
    const mockUsers = {
      student: {
        id: "student-1",
        email: "<EMAIL>",
        username: "marie.dupont",
        firstName: "Marie",
        lastName: "Dupont",
        role: "student" as const,
        profilePicture: "/placeholder.svg",
        createdAt: new Date(),
        is2FAEnabled: false
      },
      teacher: {
        id: "teacher-1",
        email: "<EMAIL>",
        username: "jean.martin",
        firstName: "Jean",
        lastName: "Martin",
        role: "teacher" as const,
        profilePicture: "/placeholder.svg",
        createdAt: new Date(),
        is2FAEnabled: false
      },
      admin: {
        id: "admin-1",
        email: "<EMAIL>",
        username: "sophie.admin",
        firstName: "Sophie",
        lastName: "Admin",
        role: "admin" as const,
        profilePicture: "/placeholder.svg",
        createdAt: new Date(),
        is2FAEnabled: false
      }
    };

    const selectedUser = mockUsers[roleId as keyof typeof mockUsers];
    if (selectedUser) {
      loginAsUser(selectedUser);
      setIsOpen(false);
    }
  };

  const getCurrentRoleInfo = () => {
    return roles.find(role => role.id === user?.role) || roles[0];
  };

  const currentRole = getCurrentRoleInfo();

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <div className={`p-1 rounded ${currentRole.color} text-white`}>
            {currentRole.icon}
          </div>
          <span>{currentRole.name}</span>
          <Settings className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Changer de Rôle
          </DialogTitle>
          <DialogDescription>
            Testez les différentes interfaces selon votre rôle dans l'établissement
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          {roles.map((role) => (
            <Card 
              key={role.id} 
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                user?.role === role.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => switchRole(role.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className={`p-2 rounded-lg ${role.color} text-white`}>
                    {role.icon}
                  </div>
                  {user?.role === role.id && (
                    <Badge className="bg-primary">
                      <Crown className="h-3 w-3 mr-1" />
                      Actuel
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-lg">{role.name}</CardTitle>
                <CardDescription className="text-sm">
                  {role.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Fonctionnalités :</h4>
                  <ul className="space-y-1">
                    {role.features.map((feature, index) => (
                      <li key={index} className="text-xs text-muted-foreground flex items-center gap-2">
                        <div className="w-1 h-1 bg-current rounded-full" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Button 
                  className="w-full mt-4" 
                  variant={user?.role === role.id ? "secondary" : "default"}
                  size="sm"
                >
                  {user?.role === role.id ? "Rôle Actuel" : `Basculer vers ${role.name}`}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Users className="h-4 w-4" />
            Utilisateur Actuel
          </h4>
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg ${currentRole.color} text-white`}>
              {currentRole.icon}
            </div>
            <div>
              <p className="font-medium">{user?.firstName} {user?.lastName}</p>
              <p className="text-sm text-muted-foreground">{user?.email}</p>
              <Badge variant="outline" className="mt-1">
                {currentRole.name}
              </Badge>
            </div>
          </div>
        </div>

        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            💡 <strong>Astuce :</strong> Ce sélecteur de rôle est uniquement pour les tests. 
            Dans un environnement de production, les rôles sont assignés par l'administration.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RoleSwitcher;
