
import { useState, ReactNode } from "react";
import { useNavigate } from "react-router-dom";
import {
  CalendarCheck,
  FileText,
  Home,
  LogOut,
  Menu,
  MessageSquare,
  Settings,
  UserRound,
  Users,
  X,
  Book,
  Bell,
  ClipboardList,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useAuth } from "@/contexts/AuthContext";
import { cn } from "@/lib/utils";
import { ThemeSwitcher } from "@/components/theme/ThemeSwitcher";
import { NotificationDropdown } from "@/components/ui/notification-dropdown";

interface DashboardLayoutProps {
  children: ReactNode;
}

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  // Utiliser un utilisateur par défaut si aucun n'est connecté
  const defaultUser = {
    firstName: "Guest",
    lastName: "User",
    role: "student",
    profilePicture: ""
  };

  // Utiliser l'utilisateur connecté ou l'utilisateur par défaut
  const currentUser = user || defaultUser;

  const sidebarItems = [
    {
      icon: Home,
      text: "Dashboard",
      href: "/dashboard",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: FileText,
      text: "News & Announcements",
      href: "/dashboard/news",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: Book,
      text: "Sections & Courses",
      href: "/dashboard/sections",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: CalendarCheck,
      text: "Calendar & Events",
      href: "/dashboard/calendar",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: ClipboardList,
      text: "Assignments",
      href: "/dashboard/assignments",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: FileText,
      text: "Resources",
      href: "/dashboard/resources",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: MessageSquare,
      text: "Forum Q&A",
      href: "/dashboard/forum",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: Users,
      text: "Work Groups",
      href: "/dashboard/groups",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: MessageSquare,
      text: "Chat",
      href: "/dashboard/chat",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: UserRound,
      text: "Profile",
      href: "/dashboard/profile",
      forRoles: ["student", "teacher", "admin"],
    },
    {
      icon: Settings,
      text: "Settings",
      href: "/dashboard/settings",
      forRoles: ["student", "teacher", "admin"],
    },
  ];

  const filteredSidebarItems = sidebarItems.filter((item) =>
    item.forRoles.includes(currentUser.role)
  );

  // Modifier la fonction de déconnexion pour gérer le cas où l'utilisateur n'est pas connecté
  const handleLogout = () => {
    if (user) {
      logout();
    }
    navigate("/login");
  };

  const handleNavigation = (href: string) => {
    navigate(href);
    setSidebarOpen(false);
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar for desktop */}
      <aside
        className={`bg-sidebar shadow-lg hidden md:flex md:flex-col w-64 transition-all duration-300 ease-in-out`}
      >
        <div className="p-4 border-b border-sidebar-border">
          <div className="flex items-center justify-center">
            <h1 className="text-xl font-bold text-edu-primary">Campus Connect</h1>
          </div>
        </div>

        <div className="flex flex-col justify-between flex-1 overflow-y-auto">
          <nav className="px-2 py-4">
            <ul className="space-y-1">
              {filteredSidebarItems.map((item) => (
                <li key={item.text}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start",
                      window.location.pathname === item.href
                        ? "bg-edu-primary bg-opacity-10 text-edu-primary"
                        : ""
                    )}
                    onClick={() => handleNavigation(item.href)}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    {item.text}
                  </Button>
                </li>
              ))}
            </ul>
          </nav>

          <div className="p-4 border-t border-sidebar-border">
            <div className="flex items-center mb-4">
              <Avatar>
                <AvatarImage src={currentUser?.profilePicture} />
                <AvatarFallback>
                  {currentUser
                    ? getInitials(`${currentUser.firstName} ${currentUser.lastName}`)
                    : "G"}
                </AvatarFallback>
              </Avatar>
              <div className="ml-3">
                <p className="text-sm font-semibold">
                  {currentUser?.firstName} {currentUser?.lastName}
                </p>
                <p className="text-xs text-muted-foreground capitalize">
                  {currentUser?.role}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              className="w-full justify-start text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 dark:hover:text-red-400"
              onClick={handleLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              {user ? "Logout" : "Login"}
            </Button>
          </div>
        </div>
      </aside>

      {/* Mobile sidebar overlay */}
      <div
        className={`fixed inset-0 bg-gray-600 bg-opacity-75 z-40 md:hidden transition-opacity duration-300 ${
          sidebarOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={() => setSidebarOpen(false)}
      ></div>

      {/* Mobile sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 bg-sidebar shadow-lg flex flex-col z-50 w-64 transition-all duration-300 ease-in-out transform md:hidden ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        }`}
      >
        <div className="p-4 border-b border-sidebar-border flex items-center justify-between">
          <h1 className="text-xl font-bold text-edu-primary">Campus Connect</h1>
          <Button
            variant="ghost"
            size="sm"
            className="rounded-full"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex flex-col justify-between flex-1 overflow-y-auto">
          <nav className="px-2 py-4">
            <ul className="space-y-1">
              {filteredSidebarItems.map((item) => (
                <li key={item.text}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start",
                      window.location.pathname === item.href
                        ? "bg-edu-primary bg-opacity-10 text-edu-primary"
                        : ""
                    )}
                    onClick={() => handleNavigation(item.href)}
                  >
                    <item.icon className="mr-3 h-5 w-5" />
                    {item.text}
                  </Button>
                </li>
              ))}
            </ul>
          </nav>

          <div className="p-4 border-t border-sidebar-border">
            <div className="flex items-center mb-4">
              <Avatar>
                <AvatarImage src={currentUser?.profilePicture} />
                <AvatarFallback>
                  {currentUser
                    ? getInitials(`${currentUser.firstName} ${currentUser.lastName}`)
                    : "G"}
                </AvatarFallback>
              </Avatar>
              <div className="ml-3">
                <p className="text-sm font-semibold">
                  {currentUser?.firstName} {currentUser?.lastName}
                </p>
                <p className="text-xs text-muted-foreground capitalize">
                  {currentUser?.role}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              className="w-full justify-start text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 dark:hover:text-red-400"
              onClick={handleLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              {user ? "Logout" : "Login"}
            </Button>
          </div>
        </div>
      </aside>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Top header */}
        <header className="bg-card shadow-sm z-10">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-6 w-6" />
              </Button>
            </div>
            <div className="flex items-center space-x-4">
              <ThemeSwitcher />
              <NotificationDropdown />
              <div className="md:hidden">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.profilePicture} />
                  <AvatarFallback>
                    {user
                      ? getInitials(`${user.firstName} ${user.lastName}`)
                      : "U"}
                  </AvatarFallback>
                </Avatar>
              </div>
            </div>
          </div>
        </header>

        {/* Main content */}
        <main className="flex-1 overflow-y-auto p-4 bg-muted/30">
          {children}
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;
