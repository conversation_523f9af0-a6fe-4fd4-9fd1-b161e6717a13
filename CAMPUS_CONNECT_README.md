# 🎓 Campus Connect - Portail Étudiant Moderne

Un portail étudiant complet avec backend Django et frontend React, incluant un forum Q&A style Stack Overflow.

## 🏗️ **Architecture Complète**

### **Frontend** (React + TypeScript)
- **Framework** : React 18 + TypeScript + Vite
- **UI** : Tailwind CSS + shadcn/ui
- **État** : Context API + Custom Hooks
- **Routing** : React Router v6
- **API** : Service API intégré avec Django
- **Port** : 8081 (configuré)

### **Backend** (Django + PostgreSQL)
- **Framework** : Django 4.2 + Django REST Framework
- **Base de données** : PostgreSQL (Railway)
- **Authentification** : JWT avec refresh tokens
- **API** : REST API complète avec documentation Swagger
- **Déploiement** : Railway (prêt pour la production)

## 🚀 **Fonctionnalités Implémentées**

### 🔐 **Authentification Complète**
- ✅ **Inscription/Connexion** avec validation
- ✅ **JWT Tokens** avec refresh automatique
- ✅ **Gestion des rôles** (Étudiant, Enseignant, Admin)
- ✅ **Profils utilisateurs** complets
- ✅ **Mode développement** avec données mock

### 💬 **Forum Q&A (Style Stack Overflow)**
- ✅ **Questions et réponses** avec backend Django
- ✅ **Système de votes** (upvote/downvote) en temps réel
- ✅ **Tags dynamiques** avec suggestions populaires
- ✅ **Recherche avancée** et filtres côté serveur
- ✅ **Acceptation de réponses** par l'auteur
- ✅ **Système de réputation** calculé automatiquement
- ✅ **Interface responsive** et intuitive

### 🎯 **Dashboards Intelligents**
- ✅ **Dashboard Étudiant** avec statistiques personnelles
- ✅ **Dashboard Admin** avec métriques globales
- ✅ **Statistiques en temps réel** depuis l'API
- ✅ **Mode sombre adaptatif**
- ✅ **États de chargement** et gestion d'erreurs

### 📊 **Analytics & Rapports**
- ✅ **Statistiques utilisateurs** (admin)
- ✅ **Métriques du forum** en temps réel
- ✅ **Suivi d'activité** et engagement
- ✅ **Rapports de performance**

## 🛠️ **Installation et Configuration**

### **1. Frontend (React)**

```bash
# Installation des dépendances
npm install

# Configuration des variables d'environnement
cp .env.local.example .env.local

# Variables d'environnement (.env.local)
VITE_API_BASE_URL=http://localhost:8000/api
VITE_API_DOCS_URL=http://localhost:8000/api/docs/
VITE_DEBUG=true

# Démarrage du serveur de développement
npm run dev
# L'application sera disponible sur http://localhost:8081
```

### **2. Backend (Django)**

```bash
# Aller dans le dossier backend
cd campus_connect_backend

# Créer un environnement virtuel
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Installer les dépendances
pip install -r requirements.txt

# Configuration des variables d'environnement
cp .env.example .env
# Éditer .env avec vos configurations

# Migrations et données initiales
python manage.py migrate
python scripts/setup.py

# Démarrage du serveur
python manage.py runserver
# L'API sera disponible sur http://localhost:8000
```

## 🔗 **Intégration Frontend-Backend**

### **Service API (src/services/api.ts)**
Le frontend utilise un service API centralisé qui :
- ✅ **Gère l'authentification** JWT automatiquement
- ✅ **Refresh les tokens** en cas d'expiration
- ✅ **Fallback vers mock data** en mode développement
- ✅ **Gestion d'erreurs** centralisée
- ✅ **Types TypeScript** complets

### **Hooks Personnalisés**
- ✅ **useForum()** - Gestion du forum Q&A
- ✅ **useQuestion()** - Détails d'une question
- ✅ **useDashboard()** - Données des dashboards
- ✅ **useTags()** - Tags populaires

### **Composants Adaptatifs**
- ✅ **États de chargement** avec skeletons
- ✅ **Gestion d'erreurs** avec retry
- ✅ **Mode offline** avec données mock
- ✅ **Animations** et transitions fluides

## 📡 **API Endpoints Principaux**

### **Authentification**
```
POST /api/auth/register/          # Inscription
POST /api/auth/login/             # Connexion
POST /api/auth/logout/            # Déconnexion
GET  /api/auth/profile/           # Profil utilisateur
PUT  /api/auth/profile/           # Modifier profil
GET  /api/auth/dashboard-data/    # Données dashboard
```

### **Forum Q&A**
```
GET  /api/forum/questions/        # Liste des questions
POST /api/forum/questions/        # Créer question
GET  /api/forum/questions/{id}/   # Détail question
POST /api/forum/questions/{id}/answers/  # Créer réponse
POST /api/forum/vote/             # Voter
POST /api/forum/answers/{id}/accept/     # Accepter réponse
GET  /api/forum/tags/             # Tags
GET  /api/forum/stats/            # Statistiques
```

### **Administration**
```
GET  /api/auth/stats/             # Stats utilisateurs (admin)
GET  /api/forum/stats/            # Stats forum (admin)
```

## 🚂 **Déploiement sur Railway**

### **Backend Django**
1. **Connecter le repository** à Railway
2. **Variables d'environnement** automatiquement configurées
3. **PostgreSQL** ajouté automatiquement
4. **Déploiement** automatique avec `Procfile`

### **Frontend React**
1. **Mettre à jour** `.env.local` avec l'URL Railway :
```env
VITE_API_BASE_URL=https://your-app.railway.app/api
```
2. **Déployer** sur Vercel/Netlify ou Railway

## 🧪 **Tests et Validation**

### **Comptes de Test**
```
Admin: <EMAIL> / admin123
Étudiant: <EMAIL> / student123
Enseignant: <EMAIL> / teacher123
```

### **Fonctionnalités à Tester**
- ✅ **Inscription/Connexion** avec validation
- ✅ **Forum Q&A** : créer question, répondre, voter
- ✅ **Dashboards** : statistiques en temps réel
- ✅ **Mode sombre** et préférences
- ✅ **Responsive design** sur mobile/desktop

## 📚 **Documentation API**

Une fois le backend démarré :
- **Swagger UI** : http://localhost:8000/api/docs/
- **ReDoc** : http://localhost:8000/api/redoc/
- **Admin Panel** : http://localhost:8000/admin/

## 🔧 **Développement**

### **Structure Frontend**
```
src/
├── components/          # Composants réutilisables
├── pages/              # Pages principales
├── hooks/              # Hooks personnalisés
├── services/           # Service API
├── contexts/           # Contexts React
├── types/              # Types TypeScript
└── lib/                # Utilitaires
```

### **Structure Backend**
```
campus_connect_backend/
├── accounts/           # Authentification & Utilisateurs
├── forum/             # Forum Q&A
├── courses/           # Gestion des cours
├── assignments/       # Devoirs
├── achievements/      # Badges/Gamification
├── analytics/         # Statistiques
└── scripts/           # Scripts utilitaires
```

## 🎉 **Prochaines Étapes**

1. **Déployer** le backend sur Railway
2. **Configurer** les variables d'environnement
3. **Tester** l'intégration complète
4. **Ajouter** les fonctionnalités manquantes :
   - Système de notifications en temps réel
   - Chat entre utilisateurs
   - Gestion des cours et devoirs
   - Système de badges avancé

## 🤝 **Contribution**

Le projet est maintenant prêt pour la production avec :
- ✅ **Backend Django** robuste et sécurisé
- ✅ **Frontend React** moderne et responsive
- ✅ **API REST** complète avec documentation
- ✅ **Authentification** JWT sécurisée
- ✅ **Forum Q&A** fonctionnel style Stack Overflow
- ✅ **Dashboards** adaptatifs selon les rôles

**Campus Connect** est maintenant une application complète prête à être utilisée par une communauté étudiante ! 🚀
