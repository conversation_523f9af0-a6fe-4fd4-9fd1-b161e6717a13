from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid

User = get_user_model()


class Department(models.Model):
    """
    Academic departments
    """
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=10, unique=True)
    description = models.TextField(blank=True)
    head_of_department = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='headed_departments'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'departments'
        ordering = ['name']
        
    def __str__(self):
        return f"{self.code} - {self.name}"


class Course(models.Model):
    """
    Academic courses - matches frontend course interface
    """
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('upcoming', 'Upcoming'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic information
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField()
    
    # Academic details
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='courses')
    credits = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(10)])
    level = models.CharField(max_length=20, choices=[
        ('undergraduate', 'Undergraduate'),
        ('graduate', 'Graduate'),
        ('postgraduate', 'Postgraduate'),
    ])
    
    # Instructor and schedule
    instructor = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='taught_courses',
        limit_choices_to={'role': 'teacher'}
    )
    schedule = models.CharField(max_length=100)  # e.g., "Mon, Wed, Fri 10:00-11:30"
    room = models.CharField(max_length=50)
    
    # Enrollment
    max_students = models.PositiveIntegerField(default=30)
    enrolled_students = models.ManyToManyField(
        User,
        through='Enrollment',
        related_name='enrolled_courses',
        limit_choices_to={'role': 'student'}
    )
    
    # Status and dates
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='upcoming')
    start_date = models.DateField()
    end_date = models.DateField()
    
    # Progress tracking
    progress_percentage = models.FloatField(default=0.0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'courses'
        ordering = ['code']
        indexes = [
            models.Index(fields=['status', 'start_date']),
            models.Index(fields=['instructor', 'status']),
        ]
        
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def enrollment_count(self):
        return self.enrolled_students.count()
    
    @property
    def is_full(self):
        return self.enrollment_count >= self.max_students
    
    @property
    def instructor_name(self):
        return self.instructor.get_full_name()


class Enrollment(models.Model):
    """
    Student enrollment in courses
    """
    GRADE_CHOICES = [
        ('A+', 'A+'), ('A', 'A'), ('A-', 'A-'),
        ('B+', 'B+'), ('B', 'B'), ('B-', 'B-'),
        ('C+', 'C+'), ('C', 'C'), ('C-', 'C-'),
        ('D+', 'D+'), ('D', 'D'), ('F', 'F'),
        ('I', 'Incomplete'), ('W', 'Withdrawn'),
    ]
    
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    
    # Enrollment details
    enrolled_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
    
    # Academic performance
    final_grade = models.CharField(max_length=2, choices=GRADE_CHOICES, blank=True)
    grade_points = models.FloatField(null=True, blank=True)
    attendance_percentage = models.FloatField(default=0.0, validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    # Progress tracking
    assignments_completed = models.PositiveIntegerField(default=0)
    total_assignments = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'enrollments'
        unique_together = ['student', 'course']
        indexes = [
            models.Index(fields=['student', 'is_active']),
            models.Index(fields=['course', 'is_active']),
        ]
        
    def __str__(self):
        return f"{self.student.get_full_name()} - {self.course.code}"
    
    @property
    def completion_percentage(self):
        if self.total_assignments == 0:
            return 0
        return (self.assignments_completed / self.total_assignments) * 100


class Lesson(models.Model):
    """
    Individual lessons within a course
    """
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='lessons')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    content = models.TextField()
    
    # Lesson details
    lesson_number = models.PositiveIntegerField()
    duration_minutes = models.PositiveIntegerField(default=60)
    
    # Scheduling
    scheduled_date = models.DateTimeField()
    is_completed = models.BooleanField(default=False)
    
    # Resources
    video_url = models.URLField(blank=True)
    slides_url = models.URLField(blank=True)
    additional_resources = models.JSONField(default=list, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'lessons'
        unique_together = ['course', 'lesson_number']
        ordering = ['course', 'lesson_number']
        
    def __str__(self):
        return f"{self.course.code} - Lesson {self.lesson_number}: {self.title}"


class Attendance(models.Model):
    """
    Student attendance tracking
    """
    STATUS_CHOICES = [
        ('present', 'Present'),
        ('absent', 'Absent'),
        ('late', 'Late'),
        ('excused', 'Excused'),
    ]
    
    student = models.ForeignKey(User, on_delete=models.CASCADE)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES)
    
    # Additional details
    check_in_time = models.DateTimeField(null=True, blank=True)
    notes = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'attendance'
        unique_together = ['student', 'lesson']
        indexes = [
            models.Index(fields=['lesson', 'status']),
            models.Index(fields=['student', 'created_at']),
        ]
        
    def __str__(self):
        return f"{self.student.get_full_name()} - {self.lesson.title} ({self.status})"


class CourseAnnouncement(models.Model):
    """
    Course announcements from instructors
    """
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='announcements')
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    
    title = models.CharField(max_length=200)
    content = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='normal')
    
    # Visibility
    is_published = models.BooleanField(default=True)
    publish_date = models.DateTimeField(auto_now_add=True)
    
    # Engagement
    views = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'course_announcements'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['course', '-created_at']),
            models.Index(fields=['priority', '-created_at']),
        ]
        
    def __str__(self):
        return f"{self.course.code} - {self.title}"


class StudyGroup(models.Model):
    """
    Student study groups for courses
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='study_groups')
    
    # Group details
    creator = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_study_groups')
    members = models.ManyToManyField(User, related_name='study_groups')
    max_members = models.PositiveIntegerField(default=8)
    
    # Meeting details
    meeting_schedule = models.CharField(max_length=100, blank=True)
    meeting_location = models.CharField(max_length=100, blank=True)
    is_virtual = models.BooleanField(default=False)
    meeting_link = models.URLField(blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    is_public = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'study_groups'
        ordering = ['-created_at']
        
    def __str__(self):
        return f"{self.name} ({self.course.code})"
    
    @property
    def member_count(self):
        return self.members.count()
    
    @property
    def is_full(self):
        return self.member_count >= self.max_members
