
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowUp, ArrowDown, MessageSquare, Search, SlidersHorizontal, Tag, Loader2 } from "lucide-react";
import { toast } from "sonner";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useAuth } from "@/contexts/AuthContext";
import { useForum, ForumFilters } from "@/hooks/useForum";
import { ForumQuestion } from "@/services/api";

const ForumPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");

  const { questions, loading, error, fetchQuestions, vote } = useForum();

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch questions when filters change
  useEffect(() => {
    const filters: ForumFilters = {
      search: debouncedSearch || undefined,
      ordering: 'activity',
    };

    // Apply tab-specific filters
    if (activeTab === "my-questions") {
      filters.my_questions = true;
    } else if (activeTab === "unanswered") {
      filters.unanswered = true;
    } else if (activeTab === "solved") {
      filters.solved = true;
    }

    fetchQuestions(filters);
  }, [activeTab, debouncedSearch, fetchQuestions]);
  // Format date to relative time (e.g., "2 days ago")
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;

    // If older than a week, return the actual date
    return date.toLocaleDateString();
  };

  const handleAskQuestion = () => {
    navigate("/dashboard/forum/ask");
  };

  const handleQuestionClick = (questionId: string) => {
    navigate(`/dashboard/forum/question/${questionId}`);
  };

  const handleVote = async (questionId: string, voteType: 1 | -1, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await vote('question', questionId, voteType);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  // Format date to relative time (e.g., "2 days ago")
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;

    // If older than a week, return the actual date
    return date.toLocaleDateString();
  };

  const handleAskQuestion = () => {
    navigate("/dashboard/forum/ask");
  };

  const handleQuestionClick = (questionId: string) => {
    navigate(`/dashboard/forum/question/${questionId}`);
  };

  // Render question item component
  const QuestionItem = ({ question }: { question: ForumQuestion }) => (
    <li
      key={question.id}
      onClick={() => handleQuestionClick(question.id)}
      className="p-4 hover:bg-muted/50 cursor-pointer"
    >
      <div className="flex items-start">
        <div className="mr-4 flex flex-col items-center space-y-1">
          <div className="flex flex-col items-center">
            <button
              className="p-1 rounded hover:bg-muted"
              onClick={(e) => handleVote(question.id, 1, e)}
              disabled={loading}
            >
              <ArrowUp className={`h-4 w-4 ${question.user_vote === 1 ? 'text-green-600 fill-current' : ''}`} />
            </button>
            <span className="text-sm font-medium py-1">{question.votes}</span>
            <button
              className="p-1 rounded hover:bg-muted"
              onClick={(e) => handleVote(question.id, -1, e)}
              disabled={loading}
            >
              <ArrowDown className={`h-4 w-4 ${question.user_vote === -1 ? 'text-red-600 fill-current' : ''}`} />
            </button>
          </div>
          {question.solved && (
            <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 dark:bg-green-900/20 dark:text-green-200 dark:border-green-800">
              Solved
            </Badge>
          )}
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-lg">{question.title}</h3>
          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
            {question.content}
          </p>
          <div className="flex flex-wrap gap-2 mt-2">
            {question.tags.map(tag => (
              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                <Tag className="h-3 w-3" />
                {tag}
              </Badge>
            ))}
          </div>
          <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={question.author.avatar} />
                <AvatarFallback>{question.author.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <span>{question.author.name}</span>
            </div>
            <span>{formatRelativeTime(question.created_at)}</span>
            <div className="flex items-center gap-1">
              <MessageSquare className="h-4 w-4" />
              <span>{question.answers} {question.answers === 1 ? 'answer' : 'answers'}</span>
            </div>
            <div className="flex items-center gap-1">
              <span>{question.views} views</span>
            </div>
          </div>
        </div>
      </div>
    </li>
  );

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Forum Q&A</h1>
            <p className="text-muted-foreground">
              Ask questions, share knowledge, and get help from the community
            </p>
          </div>
          <Button onClick={handleAskQuestion} className="mt-4 md:mt-0">
            <MessageSquare className="mr-2 h-4 w-4" />
            Ask a Question
          </Button>
        </div>

        <div className="bg-card border rounded-lg shadow">
          <div className="p-4 border-b">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search questions..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" size="icon">
                <SlidersHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
            <div className="px-4 border-b">
              <TabsList className="bg-transparent border-b-0 justify-start">
                <TabsTrigger value="all" className="data-[state=active]:border-primary data-[state=active]:border-b-2 rounded-none">
                  All Questions
                </TabsTrigger>
                <TabsTrigger value="my-questions" className="data-[state=active]:border-primary data-[state=active]:border-b-2 rounded-none">
                  My Questions
                </TabsTrigger>
                <TabsTrigger value="unanswered" className="data-[state=active]:border-primary data-[state=active]:border-b-2 rounded-none">
                  Unanswered
                </TabsTrigger>
                <TabsTrigger value="solved" className="data-[state=active]:border-primary data-[state=active]:border-b-2 rounded-none">
                  Solved
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="m-0">
              {loading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading questions...</span>
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">Error loading questions</h3>
                  <p className="text-sm text-muted-foreground mt-1">{error}</p>
                  <Button onClick={() => fetchQuestions()} className="mt-4">
                    Try Again
                  </Button>
                </div>
              ) : questions.length > 0 ? (
                <ul className="divide-y">
                  {questions.map((question) => (
                    <QuestionItem key={question.id} question={question} />
                  ))}
                </ul>
              ) : (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No questions found</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {searchQuery
                      ? "Try adjusting your search query"
                      : "Be the first to ask a question"}
                  </p>
                  <Button onClick={handleAskQuestion} className="mt-4">
                    Ask a Question
                  </Button>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="my-questions" className="m-0">
              {loading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading your questions...</span>
                </div>
              ) : questions.length > 0 ? (
                <ul className="divide-y">
                  {questions.map((question) => (
                    <QuestionItem key={question.id} question={question} />
                  ))}
                </ul>
              ) : (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No questions found</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    You haven't asked any questions yet
                  </p>
                  <Button onClick={handleAskQuestion} className="mt-4">
                    Ask a Question
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="unanswered" className="m-0">
              {loading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading unanswered questions...</span>
                </div>
              ) : questions.length > 0 ? (
                <ul className="divide-y">
                  {questions.map((question) => (
                    <QuestionItem key={question.id} question={question} />
                  ))}
                </ul>
              ) : (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No unanswered questions</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    All questions have been answered!
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="solved" className="m-0">
              {loading ? (
                <div className="flex items-center justify-center p-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading solved questions...</span>
                </div>
              ) : questions.length > 0 ? (
                <ul className="divide-y">
                  {questions.map((question) => (
                    <QuestionItem key={question.id} question={question} />
                  ))}
                </ul>
              ) : (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No solved questions</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    No questions have been marked as solved yet
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="p-4 border-t">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious href="#" />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#" isActive>1</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">2</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">3</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext href="#" />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ForumPage;
