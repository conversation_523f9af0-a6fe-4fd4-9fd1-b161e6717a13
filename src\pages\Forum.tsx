
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowUp, MessageSquare, Search, SlidersHorizontal, Tag } from "lucide-react";
import { toast } from "sonner";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { useAuth } from "@/contexts/AuthContext";

interface ForumQuestion {
  id: string;
  title: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  tags: string[];
  votes: number;
  answers: number;
  views: number;
  createdAt: string;
  solved: boolean;
}

const ForumPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Mock forum questions data
  const questions: ForumQuestion[] = [
    {
      id: "q1",
      title: "How to calculate derivatives in multivariable calculus?",
      content: "I'm struggling with understanding the concept of partial derivatives...",
      author: {
        id: "u1",
        name: "Alex Johnson",
        role: "student"
      },
      tags: ["math", "calculus", "derivatives"],
      votes: 12,
      answers: 3,
      views: 156,
      createdAt: "2025-05-10T14:30:00Z",
      solved: true
    },
    {
      id: "q2",
      title: "Best resources for learning quantum computing basics?",
      content: "I'm looking for beginner-friendly resources on quantum computing...",
      author: {
        id: "u2",
        name: "Maria Garcia",
        avatar: "/placeholder.svg",
        role: "student"
      },
      tags: ["quantum-computing", "physics", "computer-science"],
      votes: 8,
      answers: 4,
      views: 98,
      createdAt: "2025-05-11T09:15:00Z",
      solved: false
    },
    {
      id: "q3",
      title: "Help with understanding linked list implementation",
      content: "I'm trying to implement a doubly linked list in C++ but facing issues with...",
      author: {
        id: "u3",
        name: "David Kim",
        role: "student"
      },
      tags: ["programming", "data-structures", "c++"],
      votes: 5,
      answers: 2,
      views: 67,
      createdAt: "2025-05-12T16:45:00Z",
      solved: false
    },
    {
      id: "q4",
      title: "Explanation of p-value in statistical hypothesis testing",
      content: "I'm confused about the interpretation of p-values in statistical tests...",
      author: {
        id: "u4",
        name: "Sarah Williams",
        avatar: "/placeholder.svg",
        role: "teacher"
      },
      tags: ["statistics", "hypothesis-testing", "p-value"],
      votes: 15,
      answers: 5,
      views: 210,
      createdAt: "2025-05-08T11:20:00Z",
      solved: true
    },
    {
      id: "q5",
      title: "Tips for effective note-taking during lectures?",
      content: "I'm struggling to take comprehensive notes during fast-paced lectures...",
      author: {
        id: "u5",
        name: "James Wilson",
        role: "student"
      },
      tags: ["study-skills", "note-taking", "learning"],
      votes: 20,
      answers: 8,
      views: 345,
      createdAt: "2025-05-05T10:00:00Z",
      solved: true
    }
  ];

  // Get filtered questions based on active tab and search query
  const getFilteredQuestions = () => {
    let filteredQuestions = [...questions];
    
    // Filter by tab
    if (activeTab === "my-questions") {
      filteredQuestions = filteredQuestions.filter(q => q.author.id === user?.id);
    } else if (activeTab === "unanswered") {
      filteredQuestions = filteredQuestions.filter(q => q.answers === 0);
    } else if (activeTab === "solved") {
      filteredQuestions = filteredQuestions.filter(q => q.solved);
    }
    
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredQuestions = filteredQuestions.filter(q => 
        q.title.toLowerCase().includes(query) || 
        q.content.toLowerCase().includes(query) ||
        q.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    return filteredQuestions;
  };

  // Format date to relative time (e.g., "2 days ago")
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)} days ago`;
    
    // If older than a week, return the actual date
    return date.toLocaleDateString();
  };

  const handleAskQuestion = () => {
    navigate("/dashboard/forum/ask");
  };

  const handleQuestionClick = (questionId: string) => {
    navigate(`/dashboard/forum/question/${questionId}`);
  };

  const handleVote = (questionId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    toast.success("Vote recorded!");
  };

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Forum Q&A</h1>
            <p className="text-muted-foreground">
              Ask questions, share knowledge, and get help from the community
            </p>
          </div>
          <Button onClick={handleAskQuestion} className="mt-4 md:mt-0">
            <MessageSquare className="mr-2 h-4 w-4" />
            Ask a Question
          </Button>
        </div>

        <div className="bg-white rounded-lg shadow">
          <div className="p-4 border-b">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search questions..."
                  className="pl-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" size="icon">
                <SlidersHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
            <div className="px-4 border-b">
              <TabsList className="bg-transparent border-b-0 justify-start">
                <TabsTrigger value="all" className="data-[state=active]:border-primary data-[state=active]:border-b-2 rounded-none">
                  All Questions
                </TabsTrigger>
                <TabsTrigger value="my-questions" className="data-[state=active]:border-primary data-[state=active]:border-b-2 rounded-none">
                  My Questions
                </TabsTrigger>
                <TabsTrigger value="unanswered" className="data-[state=active]:border-primary data-[state=active]:border-b-2 rounded-none">
                  Unanswered
                </TabsTrigger>
                <TabsTrigger value="solved" className="data-[state=active]:border-primary data-[state=active]:border-b-2 rounded-none">
                  Solved
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="m-0">
              {getFilteredQuestions().length > 0 ? (
                <ul className="divide-y">
                  {getFilteredQuestions().map((question) => (
                    <li 
                      key={question.id}
                      onClick={() => handleQuestionClick(question.id)}
                      className="p-4 hover:bg-gray-50 cursor-pointer"
                    >
                      <div className="flex items-start">
                        <div className="mr-4 flex flex-col items-center space-y-1">
                          <button 
                            className="flex flex-col items-center p-2 rounded-md hover:bg-gray-100"
                            onClick={(e) => handleVote(question.id, e)}
                          >
                            <ArrowUp className="h-5 w-5" />
                            <span className="text-sm font-medium">{question.votes}</span>
                          </button>
                          {question.solved && (
                            <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                              Solved
                            </Badge>
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{question.title}</h3>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {question.content}
                          </p>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {question.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                <Tag className="h-3 w-3" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={question.author.avatar} />
                                <AvatarFallback>{question.author.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <span>{question.author.name}</span>
                            </div>
                            <span>{formatRelativeTime(question.createdAt)}</span>
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-4 w-4" />
                              <span>{question.answers} {question.answers === 1 ? 'answer' : 'answers'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No questions found</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {searchQuery 
                      ? "Try adjusting your search query" 
                      : activeTab === "my-questions"
                        ? "You haven't asked any questions yet"
                        : "Be the first to ask a question"}
                  </p>
                  <Button onClick={handleAskQuestion} className="mt-4">
                    Ask a Question
                  </Button>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="my-questions" className="m-0">
              {getFilteredQuestions().length > 0 ? (
                <ul className="divide-y">
                  {getFilteredQuestions().map((question) => (
                    <li
                      key={question.id}
                      onClick={() => handleQuestionClick(question.id)}
                      className="p-4 hover:bg-gray-50 cursor-pointer"
                    >
                      <div className="flex items-start">
                        <div className="mr-4 flex flex-col items-center space-y-1">
                          <button
                            className="flex flex-col items-center p-2 rounded-md hover:bg-gray-100"
                            onClick={(e) => handleVote(question.id, e)}
                          >
                            <ArrowUp className="h-5 w-5" />
                            <span className="text-sm font-medium">{question.votes}</span>
                          </button>
                          {question.solved && (
                            <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                              Solved
                            </Badge>
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{question.title}</h3>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {question.content}
                          </p>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {question.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                <Tag className="h-3 w-3" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={question.author.avatar} />
                                <AvatarFallback>{question.author.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <span>{question.author.name}</span>
                            </div>
                            <span>{formatRelativeTime(question.createdAt)}</span>
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-4 w-4" />
                              <span>{question.answers} {question.answers === 1 ? 'answer' : 'answers'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No questions found</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    You haven't asked any questions yet
                  </p>
                  <Button onClick={handleAskQuestion} className="mt-4">
                    Ask a Question
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="unanswered" className="m-0">
              {getFilteredQuestions().length > 0 ? (
                <ul className="divide-y">
                  {getFilteredQuestions().map((question) => (
                    <li
                      key={question.id}
                      onClick={() => handleQuestionClick(question.id)}
                      className="p-4 hover:bg-gray-50 cursor-pointer"
                    >
                      <div className="flex items-start">
                        <div className="mr-4 flex flex-col items-center space-y-1">
                          <button
                            className="flex flex-col items-center p-2 rounded-md hover:bg-gray-100"
                            onClick={(e) => handleVote(question.id, e)}
                          >
                            <ArrowUp className="h-5 w-5" />
                            <span className="text-sm font-medium">{question.votes}</span>
                          </button>
                          {question.solved && (
                            <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                              Solved
                            </Badge>
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{question.title}</h3>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {question.content}
                          </p>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {question.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                <Tag className="h-3 w-3" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={question.author.avatar} />
                                <AvatarFallback>{question.author.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <span>{question.author.name}</span>
                            </div>
                            <span>{formatRelativeTime(question.createdAt)}</span>
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-4 w-4" />
                              <span>{question.answers} {question.answers === 1 ? 'answer' : 'answers'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No unanswered questions</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    All questions have been answered!
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="solved" className="m-0">
              {getFilteredQuestions().length > 0 ? (
                <ul className="divide-y">
                  {getFilteredQuestions().map((question) => (
                    <li
                      key={question.id}
                      onClick={() => handleQuestionClick(question.id)}
                      className="p-4 hover:bg-gray-50 cursor-pointer"
                    >
                      <div className="flex items-start">
                        <div className="mr-4 flex flex-col items-center space-y-1">
                          <button
                            className="flex flex-col items-center p-2 rounded-md hover:bg-gray-100"
                            onClick={(e) => handleVote(question.id, e)}
                          >
                            <ArrowUp className="h-5 w-5" />
                            <span className="text-sm font-medium">{question.votes}</span>
                          </button>
                          {question.solved && (
                            <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                              Solved
                            </Badge>
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{question.title}</h3>
                          <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                            {question.content}
                          </p>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {question.tags.map(tag => (
                              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                <Tag className="h-3 w-3" />
                                {tag}
                              </Badge>
                            ))}
                          </div>
                          <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={question.author.avatar} />
                                <AvatarFallback>{question.author.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <span>{question.author.name}</span>
                            </div>
                            <span>{formatRelativeTime(question.createdAt)}</span>
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-4 w-4" />
                              <span>{question.answers} {question.answers === 1 ? 'answer' : 'answers'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="flex flex-col items-center justify-center p-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium">No solved questions</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    No questions have been marked as solved yet
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="p-4 border-t">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious href="#" />
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#" isActive>1</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">2</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationLink href="#">3</PaginationLink>
                </PaginationItem>
                <PaginationItem>
                  <PaginationEllipsis />
                </PaginationItem>
                <PaginationItem>
                  <PaginationNext href="#" />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ForumPage;
