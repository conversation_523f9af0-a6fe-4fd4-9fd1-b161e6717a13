# 🚀 Guide de Déploiement - Campus Connect

Ce guide vous accompagne pour déployer le backend Django sur Railway et connecter le frontend React.

## 📋 **Vue d'Ensemble**

### **Architecture**
- **Frontend** : React + TypeScript + Vite (Port 8081)
- **Backend** : Django + PostgreSQL + Redis (Railway)
- **Déploiement** : Railway (Backend) + Vercel/Netlify (Frontend optionnel)

### **URLs Finales**
- **Frontend Local** : http://localhost:8081
- **Backend Local** : http://localhost:8000
- **Backend Production** : https://your-app.railway.app
- **API Docs** : https://your-app.railway.app/api/docs/

---

## 🔧 **1. Préparation du Backend**

### **Structure Créée**
```
campus_connect_backend/
├── 📁 campus_connect/     # Configuration Django
├── 📁 accounts/           # Authentification & Utilisateurs
├── 📁 forum/             # Q&A Stack Overflow Style
├── 📁 courses/           # Gestion des cours
├── 📁 assignments/       # Devoirs et soumissions
├── 📁 achievements/      # Badges et gamification
├── 📁 analytics/         # Statistiques et rapports
├── 📁 notifications/     # Système de notifications
├── 📁 resources/         # Ressources pédagogiques
├── 📄 requirements.txt   # Dépendances Python
├── 📄 Procfile          # Configuration Railway
├── 📄 railway.json      # Paramètres Railway
└── 📄 runtime.txt       # Version Python
```

### **Fonctionnalités Implémentées**
- ✅ **Authentification JWT** complète
- ✅ **Forum Q&A** avec votes, tags, réputation
- ✅ **Gestion des cours** et inscriptions
- ✅ **API REST** complète avec documentation
- ✅ **Configuration Railway** prête
- ✅ **CORS** configuré pour le frontend
- ✅ **Base de données** PostgreSQL

---

## 🚂 **2. Déploiement sur Railway**

### **Étape 1 : Préparer le Repository**
```bash
# Aller dans le dossier backend
cd campus_connect_backend

# Initialiser Git si pas déjà fait
git init
git add .
git commit -m "Initial Django backend setup"

# Pousser sur GitHub
git remote add origin https://github.com/votre-username/campus-connect-backend.git
git push -u origin main
```

### **Étape 2 : Créer le Projet Railway**
1. **Aller sur** [Railway.app](https://railway.app)
2. **Se connecter** avec GitHub
3. **Cliquer** "New Project"
4. **Sélectionner** "Deploy from GitHub repo"
5. **Choisir** votre repository `campus-connect-backend`

### **Étape 3 : Configuration Automatique**
Railway détectera automatiquement :
- ✅ **Python/Django** grâce à `requirements.txt`
- ✅ **PostgreSQL** sera ajouté automatiquement
- ✅ **Variables d'environnement** seront configurées

### **Étape 4 : Variables d'Environnement**
Dans Railway Dashboard → Settings → Environment Variables :

```env
SECRET_KEY=your-super-secret-key-here-make-it-long-and-random
DEBUG=False
ALLOWED_HOST=your-app-name.railway.app
DJANGO_LOG_LEVEL=INFO
```

**Note** : `DATABASE_URL` sera automatiquement configurée par Railway.

### **Étape 5 : Déploiement**
Railway déploiera automatiquement et exécutera :
```bash
pip install -r requirements.txt
python manage.py migrate
gunicorn campus_connect.wsgi
```

### **Étape 6 : Initialiser les Données**
Une fois déployé, exécutez dans Railway Console :
```bash
python scripts/setup.py
```

---

## 🔗 **3. Configuration Frontend**

### **Étape 1 : Variables d'Environnement**
Créer/modifier `.env.local` dans le projet React :

```env
# .env.local
VITE_API_BASE_URL=https://your-app-name.railway.app/api
VITE_API_DOCS_URL=https://your-app-name.railway.app/api/docs/
```

### **Étape 2 : Service API**
Créer `src/services/api.ts` :

```typescript
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
    this.token = localStorage.getItem('access_token');
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  // Auth
  async login(credentials: { email: string; password: string }) {
    const data = await this.request('/auth/login/', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    this.token = data.tokens.access;
    localStorage.setItem('access_token', this.token);
    localStorage.setItem('refresh_token', data.tokens.refresh);
    
    return data;
  }

  async register(userData: any) {
    return this.request('/auth/register/', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getProfile() {
    return this.request('/auth/profile/');
  }

  // Forum
  async getQuestions(params?: any) {
    const query = params ? `?${new URLSearchParams(params)}` : '';
    return this.request(`/forum/questions/${query}`);
  }

  async createQuestion(questionData: any) {
    return this.request('/forum/questions/', {
      method: 'POST',
      body: JSON.stringify(questionData),
    });
  }

  async getQuestion(id: string) {
    return this.request(`/forum/questions/${id}/`);
  }

  async vote(voteData: any) {
    return this.request('/forum/vote/', {
      method: 'POST',
      body: JSON.stringify(voteData),
    });
  }

  // Courses
  async getCourses() {
    return this.request('/courses/');
  }

  // Stats (Admin)
  async getForumStats() {
    return this.request('/forum/stats/');
  }

  async getUserStats() {
    return this.request('/auth/stats/');
  }
}

export const apiService = new ApiService();
```

### **Étape 3 : Modifier AuthContext**
Mettre à jour `src/contexts/AuthContext.tsx` :

```typescript
import { createContext, useContext, useState, useEffect } from 'react';
import { apiService } from '@/services/api';

// ... rest of the context implementation using apiService
```

---

## 🧪 **4. Tests et Validation**

### **Backend Tests**
```bash
# Tests locaux
cd campus_connect_backend
python manage.py test

# Test API endpoints
curl https://your-app.railway.app/health/
curl https://your-app.railway.app/api/auth/stats/
```

### **Frontend Tests**
```bash
# Tester la connexion API
npm run dev
# Vérifier que l'app se connecte au backend Railway
```

### **Tests d'Intégration**
1. **Inscription** : Créer un compte depuis le frontend
2. **Connexion** : Se connecter avec les identifiants
3. **Forum** : Créer une question, voter, répondre
4. **Profil** : Modifier les informations utilisateur
5. **Dashboards** : Vérifier les statistiques admin

---

## 📊 **5. Monitoring et Maintenance**

### **Railway Dashboard**
- **Logs** : Surveiller les erreurs
- **Metrics** : CPU, mémoire, requêtes
- **Database** : Connexions PostgreSQL
- **Deployments** : Historique des déploiements

### **API Documentation**
- **Swagger** : https://your-app.railway.app/api/docs/
- **ReDoc** : https://your-app.railway.app/api/redoc/
- **Admin** : https://your-app.railway.app/admin/

### **Comptes de Test**
```
Admin: <EMAIL> / admin123
Étudiant: <EMAIL> / student123
Enseignant: <EMAIL> / teacher123
```

---

## 🔧 **6. Dépannage**

### **Erreurs Communes**

**1. CORS Error**
```python
# Dans settings.py, vérifier :
CORS_ALLOWED_ORIGINS = [
    "http://localhost:8081",
    "https://your-frontend-domain.com",
]
```

**2. Database Connection**
```bash
# Vérifier DATABASE_URL dans Railway
echo $DATABASE_URL
```

**3. Static Files**
```python
# settings.py
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
```

### **Logs Utiles**
```bash
# Railway CLI
railway logs

# Django logs
python manage.py shell
```

---

## ✅ **7. Checklist Final**

### **Backend Railway**
- [ ] Déployé sur Railway
- [ ] PostgreSQL configuré
- [ ] Variables d'environnement définies
- [ ] Migrations exécutées
- [ ] Données initiales créées
- [ ] API accessible

### **Frontend React**
- [ ] Variables d'environnement configurées
- [ ] Service API implémenté
- [ ] AuthContext mis à jour
- [ ] Tests de connexion réussis

### **Intégration**
- [ ] Authentification fonctionne
- [ ] Forum Q&A opérationnel
- [ ] Dashboards affichent les données
- [ ] Mode sombre adaptatif
- [ ] Navigation responsive

---

## 🎉 **Félicitations !**

Votre application **Campus Connect** est maintenant complètement déployée avec :

- ✅ **Backend Django** robuste sur Railway
- ✅ **API REST** complète avec documentation
- ✅ **Forum Q&A** style Stack Overflow
- ✅ **Authentification JWT** sécurisée
- ✅ **Base de données PostgreSQL** en production
- ✅ **Frontend React** connecté

**URLs Finales :**
- **API** : https://your-app.railway.app/api/
- **Docs** : https://your-app.railway.app/api/docs/
- **Admin** : https://your-app.railway.app/admin/

L'application est prête pour la production ! 🚀
