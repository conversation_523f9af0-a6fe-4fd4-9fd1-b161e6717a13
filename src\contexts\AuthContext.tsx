import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { User, UserRole, AuthState, LoginCredentials, RegisterData } from "../types/user";
import { apiService } from "@/services/api";
import { toast } from "sonner";

interface AuthContextType extends AuthState {
  login: (email: string, password: string, rememberMe: boolean) => Promise<boolean>;
  loginAsUser: (user: User) => void; // For testing/demo purposes
  logout: () => Promise<void>;
  register: (
    role: UserRole,
    userData: {
      email: string;
      password: string;
      firstName: string;
      lastName: string;
      username: string;
      phone?: string;
    }
  ) => Promise<boolean>;
  updateProfile: (profileData: Partial<User>) => Promise<boolean>;
  changePassword: (passwordData: {
    old_password: string;
    new_password: string;
    new_password_confirm: string;
  }) => Promise<boolean>;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  isLoading: false,
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [authState, setAuthState] = useState<AuthState>(initialState);

  // Check for existing authentication on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('access_token');
      if (token) {
        try {
          setAuthState(prev => ({ ...prev, isLoading: true }));
          const user = await apiService.getProfile();
          setAuthState({
            isAuthenticated: true,
            user,
            isLoading: false,
          });
        } catch (error) {
          console.error('Auth check failed:', error);
          // Clear invalid token
          apiService.clearToken();
          setAuthState(initialState);
        }
      }
    };

    checkAuth();
  }, []);

  // Mock users for demo/testing purposes (fallback)
  const mockUsers = [
    {
      id: 1,
      username: "marie.dupont",
      email: "<EMAIL>",
      first_name: "Marie",
      last_name: "Dupont",
      full_name: "Marie Dupont",
      role: "student" as UserRole,
      is_2fa_enabled: false,
      email_verified: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      theme_preference: "system" as const,
      initials: "MD",
    },
    {
      id: 2,
      username: "jean.martin",
      email: "<EMAIL>",
      first_name: "Jean",
      last_name: "Martin",
      full_name: "Jean Martin",
      role: "teacher" as UserRole,
      is_2fa_enabled: false,
      email_verified: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      theme_preference: "system" as const,
      initials: "JM",
    },
    {
      id: 3,
      username: "sophie.admin",
      email: "<EMAIL>",
      first_name: "Sophie",
      last_name: "Admin",
      full_name: "Sophie Admin",
      role: "admin" as UserRole,
      is_2fa_enabled: false,
      email_verified: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      theme_preference: "system" as const,
      initials: "SA",
    },
  ];

  const login = async (email: string, password: string, rememberMe: boolean): Promise<boolean> => {
    setAuthState((prev) => ({ ...prev, isLoading: true }));

    try {
      const credentials: LoginCredentials = {
        email,
        password,
        remember_me: rememberMe,
      };

      const response = await apiService.login(credentials);

      setAuthState({
        isAuthenticated: true,
        user: response.user,
        isLoading: false,
      });

      toast.success(response.message || `Welcome back, ${response.user.first_name}!`);
      return true;
    } catch (error: any) {
      console.error('Login failed:', error);

      // Fallback to mock authentication for development
      if (import.meta.env.VITE_DEBUG === 'true') {
        const user = mockUsers.find((u) => u.email === email);
        if (user && password.length > 0) {
          setAuthState({
            isAuthenticated: true,
            user,
            isLoading: false,
          });

          if (rememberMe) {
            localStorage.setItem("authUser", JSON.stringify(user));
          }

          toast.success(`Welcome back, ${user.first_name}! (Mock Mode)`);
          return true;
        }
      }

      setAuthState({
        ...initialState,
        isLoading: false,
      });

      toast.error(error.message || "Invalid email or password. Please try again.");
      return false;
    }
  };

  const loginAsUser = (user: User) => {
    setAuthState({
      isAuthenticated: true,
      user,
      isLoading: false,
    });

    // Store auth in localStorage for persistence
    localStorage.setItem("authUser", JSON.stringify(user));

    toast.success(`Switched to ${user.role}: ${user.firstName} ${user.lastName}`);
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem("authUser");
      setAuthState(initialState);
      toast.success("You have been successfully logged out.");
    }
  };

  const register = async (
    role: UserRole,
    userData: {
      email: string;
      password: string;
      firstName: string;
      lastName: string;
      username: string;
      phone?: string;
    }
  ): Promise<boolean> => {
    setAuthState((prev) => ({ ...prev, isLoading: true }));

    try {
      const registerData: RegisterData = {
        username: userData.username,
        email: userData.email,
        password: userData.password,
        password_confirm: userData.password,
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone,
        role: role,
      };

      const response = await apiService.register(registerData);

      setAuthState({
        isAuthenticated: true,
        user: response.user,
        isLoading: false,
      });

      toast.success(response.message || `Welcome, ${response.user.first_name}! Your account has been created.`);
      return true;
    } catch (error: any) {
      console.error('Registration failed:', error);

      // Fallback to mock registration for development
      if (import.meta.env.VITE_DEBUG === 'true') {
        const existingUser = mockUsers.find(
          (u) => u.email === userData.email || u.username === userData.username
        );

        if (existingUser) {
          setAuthState({
            ...initialState,
            isLoading: false,
          });
          toast.error("Email or username already exists. Please try different credentials.");
          return false;
        } else {
          // Create new mock user
          const newUser: User = {
            id: Math.floor(Math.random() * 1000),
            username: userData.username,
            email: userData.email,
            phone: userData.phone,
            role: role,
            first_name: userData.firstName,
            last_name: userData.lastName,
            full_name: `${userData.firstName} ${userData.lastName}`,
            is_2fa_enabled: false,
            email_verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            theme_preference: "system",
            initials: `${userData.firstName[0]}${userData.lastName[0]}`,
          };

          mockUsers.push(newUser);

          setAuthState({
            isAuthenticated: true,
            user: newUser,
            isLoading: false,
          });

          toast.success(`Welcome, ${newUser.first_name}! Your account has been created. (Mock Mode)`);
          localStorage.setItem("authUser", JSON.stringify(newUser));
          return true;
        }
      }

      setAuthState({
        ...initialState,
        isLoading: false,
      });

      toast.error(error.message || "Registration failed. Please try again.");
      return false;
    }
  };

  const updateProfile = async (profileData: Partial<User>): Promise<boolean> => {
    try {
      const updatedUser = await apiService.updateProfile(profileData);
      setAuthState(prev => ({
        ...prev,
        user: updatedUser,
      }));
      toast.success("Profile updated successfully!");
      return true;
    } catch (error: any) {
      console.error('Profile update failed:', error);
      toast.error(error.message || "Failed to update profile");
      return false;
    }
  };

  const changePassword = async (passwordData: {
    old_password: string;
    new_password: string;
    new_password_confirm: string;
  }): Promise<boolean> => {
    try {
      await apiService.changePassword(passwordData);
      toast.success("Password changed successfully!");
      return true;
    } catch (error: any) {
      console.error('Password change failed:', error);
      toast.error(error.message || "Failed to change password");
      return false;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...authState,
        login,
        loginAsUser,
        logout,
        register,
        updateProfile,
        changePassword,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
